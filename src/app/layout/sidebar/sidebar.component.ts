import { Component, HostListener, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '@app/modules/auth/auth.service';
import { Dialog } from '@angular/cdk/dialog';
import { TranslocoService } from "@ngneat/transloco";
import { MenuItem, MenuSection } from '../../data/models/sidebar.interface';
import { PaymentService } from '@app/data/services/payment.service';
import { SettingsComponent } from '@app/modules/settings/pages/settings/settings.component';
import { BalanceComponent } from '@app/modules/payment/dialogs/balance/balance.component';
import { PaymentComponent } from '@app/modules/payment/dialogs/payment/payment.component';
import { HelpCenterComponent } from '@app/shared/components/help-center/help-center.component';
import { AdminService } from '@app/data/services/admin.service';
import { SnotifyService } from 'ng-alt-snotify';
import { trigger, transition, style, animate } from '@angular/animations';
import { CorporateManagementComponent } from '@app/shared/components/corporate-management/corporate-management.component';
import { ReferralComponent } from '@app/shared/components/referral/referral.component'; // Import ReferralComponent
import { CreditUpdateService } from '@app/data/services/credit-update.service';
import { Subscription } from 'rxjs';
import { ExploreComponent } from '@app/modules/explore/pages/explore/explore.component';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
  animations: [
    trigger('slideIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(10px)' }),
        animate('200ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ])
    ])
  ]
})
export class SidebarComponent implements OnInit, OnDestroy {
  private creditUpdateSubscription: Subscription;

  constructor(
    private auth: AuthService,
    private router: Router,
    private langService: TranslocoService,
    private payment: PaymentService,
    private dialog: Dialog,
    private admin: AdminService,
    private snotifyService: SnotifyService,
    private creditUpdateService: CreditUpdateService
  ) {

    const names = this.username.split(' ');
    const firstName = names[0];
    const lastName = names[names.length - 1];
    const firstInitial = firstName.charAt(0);
    const lastInitial = lastName.charAt(0);
    this.fullname = firstInitial + lastInitial;
  }


  isCollapsed = false;
  userRole: string = '';
  isAdmin: boolean = false;
  menuSections: MenuSection[] = [
    {
      title: 'titles.workspace',
      items: [
        { icon: 'lucideLayers', name: 'workspace.projects', route: '/projects', tooltip: 'sidebar.tooltip.projects' },
        { icon: 'lucideNotepadText', name: 'workspace.reports', route: '/reports', tooltip: 'sidebar.tooltip.reports' }
      ]
    },
    {
      title: 'titles.social',
      items: [
        {
          icon: 'lucideUserRoundPlus',
          name: 'social.invite_friends',
          action: 'dialog',
          component: ReferralComponent,
          tooltip: 'sidebar.tooltip.invite_friends'
        },
        {
          icon: 'lucideCompass',
          name: 'social.explore',
          route: '/explore',
          component: ExploreComponent,
          tooltip: 'sidebar.tooltip.explore'
        }
      ]
    }
  ];


  isRole(role: 'admin' | 'analyzer' | 'proxy_user' | 'unit_manager') {
    if (localStorage.getItem('roles')) {
      return JSON.parse(localStorage.getItem('roles')).filter((userRole) => userRole.name == role).length > 0;
    } else {
      this.auth.logout().subscribe(
        (response) => {
          return false;
        },
        (error) => {
          console.error('Error logging out', error);
        }
      );
      return false
    }
  }

  username: string = localStorage.getItem('username') ? localStorage.getItem('username') : 'User';

  user = {
    name: this.username,
    role: '',
    icon: 'lucideUser'
  };

  currentRoute: string;
  fullname: string;
  activeLang = "tr";

  // ------------------------
  // Eklenen kredi hesaplama bölümü:
  // ------------------------
  packages: any = [];
  showSkeleton = true;
  totalCredit: number;
  remainingCreditRatio: number = 0;
  usageHistory: any = [];
  ratio: number = 0;
  // ------------------------

  // Profile menu property
  showProfileMenu: boolean = false;

  loadUserRole(): void {
    const rolesData = localStorage.getItem('roles');
    const userLang = localStorage.getItem('activeLang') || 'en';

    if (rolesData) {
      try {
        // JSON formatına çevir
        const rolesArray = JSON.parse(rolesData);

        // Eğer array mevcutsa ve en az bir elemanı varsa, 0. elemanın name'ini al
        if (Array.isArray(rolesArray) && rolesArray.length > 0) {
          let roleName = rolesArray[0].name;

          // Eğer rol "analyzer" ise ve dil Türkçe ise "Araştırmacı" olarak ayarla
          if (roleName.toLowerCase() === 'analyzer' && userLang === 'tr') {
            this.user.role = 'Araştırmacı';
          } else {
            this.user.role = roleName.charAt(0).toLocaleUpperCase() + roleName.slice(1);
          }
        }
      } catch (error) {
        console.error("LocalStorage 'roles' verisi düzgün okunamadı:", error);
      }
    }
  }

  clickIstabot() {
    const url = window.location.origin;
    window.location.href = url;
  }

  handleItemClick(item: MenuItem): void {
    if (item.route) {
      // Rota değişikliği öncesinde mevcut sayfa bilgisini temizle
      if (item.route === '/reports') {
        localStorage.setItem('currentPage', 'reports');
      } else if (item.route === '/projects') {
        localStorage.setItem('currentPage', 'projects');
      }

      // Route navigation
      this.router.navigate([item.route]);
    } else if (item.action === 'dialog' && item.component) {
      // Dialog açma
      this.dialog.open(item.component, {
        // width: '80vw',
      });
    }
  }

  openSettingsModal() {
    this.dialog.open(SettingsComponent);
  }

  openBalanceModal() {
    this.dialog.open(BalanceComponent, {
      width: '80vw'
    });
  }

  openPaymentModal() {
    this.dialog.open(PaymentComponent);
  }

  openHelpCenter() {
    this.dialog.open(HelpCenterComponent, {
    });
  }
  openCorporateManagement() {
    this.dialog.open(CorporateManagementComponent);
  }

  // Toggle profile menu with event stopPropagation
  toggleProfileMenu(event?: MouseEvent): void {
    // Prevent event propagation so it doesn't trigger the document click handler
    if (event) {
      event.stopPropagation();
    }
    this.showProfileMenu = !this.showProfileMenu;
  }

  /**
   * PaymentService'den kullanıcı kredi bilgilerini çeker.
   * Ek olarak, gelen verilerle paketlerin kalan kredi oranını hesaplayan metodu çağırır.
   */
  getUserCredits() {
    this.payment.getUserCredits().subscribe({
      next: (user_credits) => {
        // Toplam kredi bilgisini alıyoruz.
        this.totalCredit = user_credits.merged.total_remaining_credit;

        // Kalan kredisi 0'dan büyük olan paketleri filtreleyip,
        // oluşturulma tarihine göre (en yeni önce) sıralıyoruz.
        this.packages = user_credits.data
          .filter(usage => usage.remaining_credit > 0)
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

        // Paketlerin kalan kredilerinin toplamının, toplam krediye oranını hesaplıyoruz.
        this.calculateRemainingCreditRatio();
      },
      complete: () => {
        this.showSkeleton = false;
      },
      error: (err) => {
        console.error('Kredi bilgileri çekilirken hata oluştu:', err);
      }
    });
  }

  calculateRemainingCreditRatio() {
    const totalCredit = this.packages.reduce((sum, pkg) => sum + pkg.credit, 0);
    const totalRemainingCredit = this.packages.reduce((sum, pkg) => sum + pkg.remaining_credit, 0);
    this.ratio = totalRemainingCredit / totalCredit;
  }

  ngOnInit(): void {
    this.langService.setActiveLang(this.activeLang);

    // Sidebar durumunu localStorage'dan al
    const storedState = localStorage.getItem('sidebarCollapsed');
    if (storedState !== null) {
      this.isCollapsed = storedState === 'true';
    }

    // Kullanıcı rolünü yükle
    this.loadUserRole();

    // Kullanıcı kredilerini yükle
    this.getUserCredits();

    // Subscribe to credit updates
    this.creditUpdateSubscription = this.creditUpdateService.creditUpdated$.subscribe(() => {
      this.getUserCredits();
    });
  }

  ngOnDestroy(): void {
    // Unsubscribe to prevent memory leaks
    if (this.creditUpdateSubscription) {
      this.creditUpdateSubscription.unsubscribe();
    }
  }

  sidebarState: boolean = false;
  toggleSidebar() {
    this.isCollapsed = !this.isCollapsed;

    // Değişikliği LocalStorage'a kaydet
    localStorage.setItem('sidebarCollapsed', this.isCollapsed.toString());
  }

  logout() {
    this.auth.logout().subscribe({
      next: (v) => console.log(v),
      error: (e) => console.error(e),
      complete: () => {
        // Oturum kapatma işlemi tamamlandıktan sonra login sayfasına yönlendirme.
        this.router.navigateByUrl('/login');
      },
    });
  }

  // Document click handler for closing the menu when clicking outside
  @HostListener('document:click', ['$event'])
  handleClick(event: Event): void {
    const clickedElement = event.target as HTMLElement;

    // Profile button click check - don't close if clicking the profile button
    const profileButton = document.querySelector('.profile-button');
    const profileMenu = document.querySelector('.profile-menu-fullview');

    // Only process clicks outside the profile areas when menu is open
    if (this.showProfileMenu &&
      profileButton &&
      profileMenu &&
      !profileButton.contains(event.target as Node) &&
      !profileMenu.contains(event.target as Node)) {
      this.showProfileMenu = false;
    }

    // Handle original sidebar state if needed
    if (!clickedElement.classList.contains('sidebar')) {
      this.sidebarState = false;
    }
  }

  switchBack() {
    this.admin.switchBack().subscribe({
      next: (data) => {
        // Orijinal kullanıcı bilgisini temizle
        localStorage.removeItem('original_user');

        // ProjectStateService'i temizlemek için AdminService'teki clearProjectState metodu kullanılıyor
        // Ancak yine de localStorage'daki currentPage değerini kontrol edelim
        if (localStorage.getItem('currentPage')) {
          localStorage.removeItem('currentPage');
        }

        // Başarılı mesajı göster
        this.snotifyService.success(data.body.message, {
          position: 'centerBottom',
          timeout: 1000,
          pauseOnHover: false,
        }).on('beforeHide', (toast) => {
          // Admin kullanıcılar sayfasına yönlendir
          this.router.navigateByUrl('/admin/users');
          return true;
        });
      },
      error: (e) => {
        this.snotifyService.error(e.error.message, {
          position: 'centerBottom',
          timeout: 1000,
          pauseOnHover: false,
        });
      }
    });
  }

  get isSwitchedAccount(): boolean {
    return !!localStorage.getItem('original_user');
  }
}