import { <PERSON>pe, PipeTransform } from '@angular/core';
import { Dom<PERSON>anitizer, SafeHtml } from '@angular/platform-browser';

@Pipe({
  name: 'formulaFormatter'
})
export class FormulaFormatterPipe implements PipeTransform {

  constructor(private sanitizer: DomSanitizer) {}

  transform(text: string): SafeHtml {
    if (!text) return '';

    let formattedText = text;

    // Sadece temel formül kalıplarını uygula
    const formulaPatterns = [
      // Üst simge sayıları (örn: R², F², t²)
      {
        pattern: /([A-Za-z]+)²/g,
        replacement: '<span class="formula-variable">$1</span><sup class="formula-superscript">²</sup>'
      },
      // Alt simge sayıları (örn: H₀, H₁)
      {
        pattern: /([A-Za-z]+)([₀₁₂₃₄₅₆₇₈₉])/g,
        replacement: '<span class="formula-variable">$1</span><sub class="formula-subscript">$2</sub>'
      },
      // <PERSON><PERSON><PERSON><PERSON><PERSON> (örn: p < 0.05, p = 0.001)
      {
        pattern: /(p|r|t|F|χ²|df)\s*([<>=≤≥≠])\s*([\d.,]+)/g,
        replacement: '<span class="formula-stat-symbol">$1</span> <span class="formula-operator">$2</span> <span class="formula-value">$3</span>'
      }
    ];

    // Her formül kalıbını uygula
    formulaPatterns.forEach(pattern => {
      formattedText = formattedText.replace(pattern.pattern, pattern.replacement);
    });

    // Formül bloklarını tanımla ve sadece altına boşluk ekle
    formattedText = this.formatFormulaBlocks(formattedText);

    return this.sanitizer.bypassSecurityTrustHtml(formattedText);
  }

  private formatFormulaBlocks(text: string): string {
    // Formül bloklarını tanımla ve sadece altına boşluk ekle (örn: "H₀: μ₁ = μ₂")
    const formulaBlockPattern = /([A-Za-z]+[₀₁₂₃₄₅₆₇₈₉]*\s*:\s*[^.!?\n]+)/g;

    return text.replace(formulaBlockPattern, (match) => {
      return `${match}<br><br>`;
    });
  }
}
