import { <PERSON>pe, PipeTransform } from '@angular/core';
import { Dom<PERSON>anitizer, SafeHtml } from '@angular/platform-browser';

@Pipe({
  name: 'formulaFormatter'
})
export class FormulaFormatterPipe implements PipeTransform {

  constructor(private sanitizer: DomSanitizer) {}

  transform(text: string): SafeHtml {
    if (!text) return '';

    // Formül kalıplarını tanımla
    const formulaPatterns = [
      // Matematiks<PERSON> formüller (örn: x² + y² = z²)
      {
        pattern: /([a-zA-ZαβγδεζηθικλμνξοπρστυφχψωΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩ]+)([²³⁴⁵⁶⁷⁸⁹⁰¹])/g,
        replacement: '<span class="formula-variable">$1</span><sup class="formula-superscript">$2</sup>'
      },
      // Üst simge sayıları (örn: R², F², t²)
      {
        pattern: /([A-Za-z]+)²/g,
        replacement: '<span class="formula-variable">$1</span><sup class="formula-superscript">²</sup>'
      },
      // Alt simge sayıları (örn: H₀, H₁)
      {
        pattern: /([A-Za-z]+)([₀₁₂₃₄₅₆₇₈₉])/g,
        replacement: '<span class="formula-variable">$1</span><sub class="formula-subscript">$2</sub>'
      },
      // Yunan harfleri ve matematiksel semboller
      {
        pattern: /(α|β|γ|δ|ε|ζ|η|θ|ι|κ|λ|μ|ν|ξ|ο|π|ρ|σ|τ|υ|φ|χ|ψ|ω|Α|Β|Γ|Δ|Ε|Ζ|Η|Θ|Ι|Κ|Λ|Μ|Ν|Ξ|Ο|Π|Ρ|Σ|Τ|Υ|Φ|Χ|Ψ|Ω)/g,
        replacement: '<span class="formula-greek">$1</span>'
      },
      // İstatistiksel değerler (örn: p < 0.05, p = 0.001)
      {
        pattern: /(p|r|t|F|χ²|df)\s*([<>=≤≥≠])\s*([\d.,]+)/g,
        replacement: '<span class="formula-stat-symbol">$1</span> <span class="formula-operator">$2</span> <span class="formula-value">$3</span>'
      },
      // Matematiksel operatörler
      {
        pattern: /([<>=≤≥≠±∓×÷∑∏∫∂∇∆∞])/g,
        replacement: '<span class="formula-operator">$1</span>'
      },
      // Parantez içindeki formüller
      {
        pattern: /\(([^)]+)\)/g,
        replacement: '<span class="formula-parentheses">($1)</span>'
      }
    ];

    let formattedText = text;

    // Her formül kalıbını uygula
    formulaPatterns.forEach(pattern => {
      formattedText = formattedText.replace(pattern.pattern, pattern.replacement);
    });

    // Formül bloklarını tanımla ve özel stil uygula
    formattedText = this.formatFormulaBlocks(formattedText);

    return this.sanitizer.bypassSecurityTrustHtml(formattedText);
  }

  private formatFormulaBlocks(text: string): string {
    // Formül bloklarını tanımla (örn: "H₀: μ₁ = μ₂" gibi)
    const formulaBlockPattern = /([A-Za-z]+[₀₁₂₃₄₅₆₇₈₉]*\s*:\s*[^.!?]+)/g;
    
    return text.replace(formulaBlockPattern, (match) => {
      return `<div class="formula-block">${match}</div>`;
    });
  }
}
