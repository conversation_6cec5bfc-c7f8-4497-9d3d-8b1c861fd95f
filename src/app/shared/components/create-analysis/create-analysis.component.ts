import { ChangeDetectorRef, Component, Inject, Input, Output, EventEmitter, OnInit, ViewChildren, QueryList } from '@angular/core';
import { trigger, transition, style, animate } from '@angular/animations';
import { AnalysisHelperService } from '@data/helper/analysis.helper.service';
import { Dialog, DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { Router } from '@angular/router';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { TranslocoService } from '@ngneat/transloco';
import { SnotifyService } from 'ng-alt-snotify';
import { ConfirmComponent } from '@app/shared/components/confirm/confirm.component';
import { DatasetViewComponent } from '@app/shared/components/dataset-view/dataset-view.component';
import { DatasetService } from '@app/data/services/dataset.service';
import { PaymentComponent } from '@app/modules/payment/dialogs/payment/payment.component';
import { CreditUpdateService } from '@app/data/services/credit-update.service';

interface Variable {
  id: number;
  name: string;
  type: string;
  description: string;
  mean?: number;
  stdDev?: number;
  categories?: string[];
  measure?: string;
  value_labels?: any;
  computed?: boolean;
  referenceValue?: string;
  selected?: boolean;
  disabled?: boolean;
  disabledReason?: string; // Değişkenin neden devre dışı bırakıldığını belirtir
  header?: string;
}

interface AnalysisInfo {
  title: string;
  description: string;
  helpText: string;
  examples: string[];
  referenceValue?: boolean;
}

@Component({
  selector: 'app-create-analysis',
  templateUrl: './create-analysis.component.html',
  styleUrls: ['./create-analysis.component.scss'],
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('300ms ease-in', style({ opacity: 1 }))
      ])
    ]),
    trigger('slideInOut', [
      transition(':enter', [
        style({ transform: 'translateX(100%)', opacity: 0 }),
        animate('300ms ease-out', style({ transform: 'translateX(0)', opacity: 1 }))
      ]),
      transition(':leave', [
        style({ transform: 'translateX(0)', opacity: 1 }),
        animate('300ms ease-in', style({ transform: 'translateX(-100%)', opacity: 0 }))
      ])
    ])
  ]
  ,
})
export class CreateAnalysisComponent implements OnInit {
  @Output() report_detail = new EventEmitter<any>();

  // Analysis info and clone variables from dialog data
  analysis_info: any;
  clone_variables: any;
  isCloned: boolean = false;
  clonedReportId: string | null = null;

  // Current analysis type and step
  currentAnalysisType: string = '';
  currentStep: number = 1;
  maxSteps: number = 1;
  reviewStep: number = 2; // Review step is always one more than the max step
  showStepWarning: boolean = false; // Adım uyarısını göstermek için
  warningMessage: string = ''; // Uyarı mesajı
  activeTooltipStep: number | null = null; // Aktif tooltip adımı
  activeValueLabelsVariable: number | null = null; // Değer etiketleri gösterilen değişken ID'si
  activeReviewValueLabelsVariable: number | null = null; // Gözden geçir adımında değer etiketleri gösterilen değişken ID'si
  activeReviewTimesForDefine: number | null = null; // Gözden geçir adımında tanım zamanları gösterilen değişken ID'si
  activeStepValueLabelsVariable: number | null = null; // Adımlar listesinde değer etiketleri gösterilen değişken ID'si
  stepValueLabelsTooltipPosition: { top: string, left: string } = { top: '0px', left: '0px' }; // Adımlar listesindeki değer etiketleri tooltip'inin konumu

  // Analysis configuration
  analysisConfiguration: any = null;

  // Variables and configurations
  datasetVariables: Variable[] = [];
  filteredVariables: any = {};
  selectedVariables: any = {};
  variableDisplayMode: 'name' | 'header' | 'both' = 'both';
  measureFilters: { [key: string]: boolean } = {
    'Scale': true,
    'Ordinal': true,
    'Nominal': true
  };
  searchTerm: string = '';

  // Dependent Analizi için değişkenler
  multiDefineList: any[] = [];
  defineTimes: any[] = [];
  multiDefine: any = {
    ind: 1,
    defineTr: null,
    defineEn: null,
    timeTable: [],
    inset_ind: null
  };
  multiDefineTimes: any = {
    ind: 1,
    showSplitList: false,
    sub: [
      {
        id: 1,
        variable: null,
        variableId: null,
        showVariableList: false,
        timeTr: null,
        timeEn: null,
        disabled: false,
        searchTerm: '',
        showDropdown: false,
        filteredVariables: [],
        dropdownPosition: 'bottom' // default position
      },
      {
        id: 2,
        variable: null,
        variableId: null,
        showVariableList: false,
        timeTr: null,
        timeEn: null,
        disabled: false,
        searchTerm: '',
        showDropdown: false,
        filteredVariables: [],
        dropdownPosition: 'bottom' // default position
      },
    ]
  };

  // Modals
  showCopyTimesModal = false;
  showCloneOptionsModal = false;


  // Additional options for specific analysis types
  additionalOptions: any = {};

  // Ki-kare analizi için rapor türü seçimi (satıra göre veya sütuna göre)
  selectedReportType: string = 'row'; // Varsayılan olarak satıra göre
  showChiSquareOptions: boolean = false; // Ki-kare rapor türü seçimi gösterilsin mi?

  // Ortalama karşılaştırma analizi için zaman içerip içermediği bilgisi
  isInvolveTime: boolean = false; // Varsayılan olarak zaman içermiyor
  showTimeDataQuestion: boolean = false; // Zaman sorusu gösterilsin mi?

  // Tek grup analizi için referans değeri alanı gösterilsin mi?
  showReferenceValueFields: boolean = false;

  // Dependent analizi için metodlar
  getFilteredVariablesForDependent(): Variable[] {
    if (!this.datasetVariables) return [];

    // Sadece Scale tipindeki değişkenleri filtrele
    return this.datasetVariables.filter(v => v.measure === 'Scale');
  }

  /**
   * Belirli bir zaman için arama terimini kullanarak filtrelenmiş değişkenleri döndürür
   * @param time Zaman nesnesi
   * @returns Filtrelenmiş değişkenler listesi
   */
  getFilteredVariablesForTime(time: any): Variable[] {
    // Önce tüm Scale tipindeki değişkenleri al
    let variables = this.getFilteredVariablesForDependent();

    // Diğer zaman girişlerinde seçilmiş değişkenleri işaretle
    if (this.multiDefineTimes && this.multiDefineTimes.sub) {
      // Mevcut zaman dışındaki diğer zamanlarda seçilmiş değişkenlerin ID'lerini al
      const selectedVariableIds = this.multiDefineTimes.sub
        .filter((t: any) => t.id !== time.id && t.variableId !== null && t.variable !== null)
        .map((t: any) => t.variable ? t.variable.id : t.variableId);

      // Seçilmiş değişkenleri disable olarak işaretle
      variables = variables.map(v => {
        if (selectedVariableIds.includes(v.id)) {
          return { ...v, disabled: true };
        }
        return v;
      });
    }

    // Eğer arama terimi varsa, filtreleme yap
    if (time.searchTerm && time.searchTerm.trim() !== '') {
      const searchTerm = time.searchTerm.trim().toLocaleLowerCase('tr-TR');
      variables = variables.filter(v =>
        v.name.toLocaleLowerCase('tr-TR').includes(searchTerm) ||
        (v.header && v.header.toLocaleLowerCase('tr-TR').includes(searchTerm))
      );
    }

    return variables;
  }



  /**
   * Belirli bir zaman için değişkenleri filtreler
   * @param time Zaman nesnesi
   */
  filterVariablesForTime(time: any): void {
    if (!time) return;

    // getFilteredVariablesForTime metodunu kullanarak filtrelenmiş değişkenleri al
    // Bu metod diğer zamanlarda seçilmiş değişkenleri disable olarak işaretliyor
    const variables = this.getFilteredVariablesForTime(time);

    // Filtrelenmiş değişkenleri zaman nesnesine ata
    time.filteredVariables = variables;
  }

  /**
   * Değişken seçildiğinde zaman için değişken ve variableId'yi günceller
   * @param variable Seçilen değişken nesnesi
   * @param time Güncellenecek zaman nesnesi
   */
  selectVariable(variable: any, time: any): void {
    if (variable) {
      // Değişken seçildiğinde, variable ve variableId'yi güncelle
      time.variable = variable;
      time.variableId = variable.id;

      // Tüm zamanlar için filtrelenmiş değişkenleri güncelle
      if (this.multiDefineTimes && this.multiDefineTimes.sub) {
        this.multiDefineTimes.sub.forEach((t: any) => {
          this.filterVariablesForTime(t);
        });
      }
    }
  }

  /**
   * Seçili değişkeni ve zaman isimlerini temizler
   * @param time Zaman nesnesi
   */
  clearVariable(time: any): void {
    // Değişken seçimini temizle
    time.variable = null;
    time.variableId = null;
    time.showDropdown = false;

    // Zaman isimlerini de temizle
    time.timeTr = null;
    time.timeEn = null;

    // Tüm zamanlar için filtrelenmiş değişkenleri güncelle
    if (this.multiDefineTimes && this.multiDefineTimes.sub) {
      this.multiDefineTimes.sub.forEach((t: any) => {
        this.filterVariablesForTime(t);
      });
    }
  }

  /**
   * Dropdown açıldığında arama kutusuna odaklanır
   * @param time Zaman nesnesi
   */
  focusSearchInput(time: any): void {
    if (time.showDropdown) {
      // Dropdown açıldığında arama kutusuna odaklan
      setTimeout(() => {
        const searchInput = document.getElementById('search-input-' + time.id);
        if (searchInput) {
          searchInput.focus();
        }
      }, 100);
    }
  }

  /**
   * Dropdown'u açar/kapatır ve konumunu belirler
   * @param time Zaman nesnesi
   */
  toggleDropdown(time: any): void {
    // Dropdown durumunu değiştir
    time.showDropdown = !time.showDropdown;

    if (time.showDropdown) {
      // Dropdown açıldığında filtrelenmiş değişkenleri güncelle
      this.filterVariablesForTime(time);

      // Dropdown açıldığında konumunu belirle
      this.determineDropdownPosition(time);

      // Arama kutusuna odaklan
      this.focusSearchInput(time);
    }
  }

  /**
   * Dropdown'un konumunu belirler (yukarı veya aşağı)
   * @param time Zaman nesnesi
   */
  determineDropdownPosition(time: any): void {
    setTimeout(() => {
      // Dropdown butonunun DOM elementini bul
      const dropdownButton = document.getElementById('dropdown-button-' + time.id);

      if (dropdownButton) {
        // Butonun ekrandaki konumunu al
        const rect = dropdownButton.getBoundingClientRect();

        // Ekranın altında kalan boşluk
        const spaceBelow = window.innerHeight - rect.bottom;

        // Dropdown'un yaklaşık yüksekliği (60 item * 30px + 60px arama kutusu)
        const dropdownHeight = Math.min(time.filteredVariables.length * 30 + 60, 240); // max-h-60 = 240px

        // Eğer altta yeterli boşluk yoksa, yukarı aç
        if (spaceBelow < dropdownHeight && rect.top > dropdownHeight) {
          time.dropdownPosition = 'top';
        } else {
          time.dropdownPosition = 'bottom';
        }

        // Değişikliklerin algılanması için change detection'u tetikleyelim

      }
    }, 0);
  }

  isValidMultiDefine(): boolean {
    // Grup adı kontrolü - Türkçe ve İngilizce ad zorunlu
    if (!this.multiDefine?.defineTr || this.multiDefine.defineTr.trim() === '' ||
      !this.multiDefine?.defineEn || this.multiDefine.defineEn.trim() === '') {
      return false;
    }

    // En az 2 zaman tanımlanmış mı?
    if (!this.multiDefineTimes?.sub || this.multiDefineTimes.sub.length < 2) {
      return false;
    }

    // Tüm zamanlar için Türkçe ve İngilizce isimler girilmiş mi?
    const allTimesHaveNames = this.multiDefineTimes.sub.every((time: any) =>
      time.timeTr && time.timeTr.trim() !== '' &&
      time.timeEn && time.timeEn.trim() !== ''
    );

    if (!allTimesHaveNames) {
      return false;
    }

    // Tüm zamanlar için değişken seçilmiş mi?
    const allTimesHaveVariables = this.multiDefineTimes.sub.every((time: any) =>
      time.variable !== null && time.variableId !== null
    );

    if (!allTimesHaveVariables) {
      return false;
    }

    return true;
  }

  // Yarım kalan tanımlama var mı kontrol et
  hasIncompleteDefine(): boolean {
    // Eğer multiDefine veya multiDefineTimes tanımlı değilse, yarım kalan tanımlama yok
    if (!this.multiDefine || !this.multiDefineTimes) {
      return false;
    }

    // Grup adı girilmiş mi?
    const hasGroupName = this.multiDefine.defineTr && this.multiDefine.defineTr.trim() !== '';

    // Herhangi bir zaman adı girilmiş mi?
    const hasAnyTimeName = this.multiDefineTimes.sub && this.multiDefineTimes.sub.some((time: any) =>
      (time.timeTr && time.timeTr.trim() !== '') ||
      (time.timeEn && time.timeEn.trim() !== '')
    );

    // Herhangi bir değişken seçilmiş mi?
    const hasAnyVariable = this.multiDefineTimes.sub && this.multiDefineTimes.sub.some((time: any) =>
      time.variable !== null || time.variableId !== null
    );

    // Eğer herhangi bir veri girilmişse ve tanım geçerli değilse, yarım kalan tanımlama var
    return (hasGroupName || hasAnyTimeName || hasAnyVariable) && !this.isValidMultiDefine();
  }

  addDefineToList(): void {
    // Grup adı kontrolü - Türkçe ve İngilizce ad zorunlu
    if (!this.multiDefine?.defineTr || this.multiDefine.defineTr.trim() === '' ||
      !this.multiDefine?.defineEn || this.multiDefine.defineEn.trim() === '') {
      this.warningMessage = 'Tanım için Türkçe ve İngilizce isim girmeniz zorunludur.';
      this.showStepWarning = true;
      setTimeout(() => {
        this.showStepWarning = false;
        this.warningMessage = '';
      }, 5000);
      return;
    }

    // En az 2 zaman tanımlanmış mı?
    if (!this.multiDefineTimes?.sub || this.multiDefineTimes.sub.length < 2) {
      this.warningMessage = 'En az 2 zaman tanımlamanız zorunludur.';
      this.showStepWarning = true;
      setTimeout(() => {
        this.showStepWarning = false;
        this.warningMessage = '';
      }, 5000);
      return;
    }

    // Tüm zamanlar için Türkçe ve İngilizce isimler girilmiş mi?
    const allTimesHaveNames = this.multiDefineTimes.sub.every((time: any) =>
      time.timeTr && time.timeTr.trim() !== '' &&
      time.timeEn && time.timeEn.trim() !== ''
    );

    if (!allTimesHaveNames) {
      this.warningMessage = 'Tüm zamanlar için Türkçe ve İngilizce isim girmeniz zorunludur.';
      this.showStepWarning = true;
      setTimeout(() => {
        this.showStepWarning = false;
        this.warningMessage = '';
      }, 5000);
      return;
    }

    // Tüm zamanlar için değişken seçilmiş mi?
    const allTimesHaveVariables = this.multiDefineTimes.sub.every((time: any) =>
      time.variable !== null && time.variableId !== null
    );

    if (!allTimesHaveVariables) {
      this.warningMessage = 'Tüm zamanlar için değişken seçmeniz zorunludur.';
      this.showStepWarning = true;
      setTimeout(() => {
        this.showStepWarning = false;
        this.warningMessage = '';
      }, 5000);
      return;
    }

    // Zaman tablosunu oluştur
    const timeTable = {
      sub: this.multiDefineTimes?.sub?.map((time: any) => ({
        id: time.id,
        timeTr: time.timeTr,
        timeEn: time.timeEn || time.timeTr, // İngilizce tanım yoksa Türkçe kullan
        variable: time.variable,
        variableId: time.variableId
      })) || []
    };

    // Yeni tanım oluştur
    const newDefine = {
      ind: this.multiDefineList?.length + 1 || 1,
      defineTr: this.multiDefine?.defineTr,
      defineEn: this.multiDefine?.defineEn || this.multiDefine?.defineTr, // İngilizce tanım yoksa Türkçe kullan
      timeTable: timeTable,
      inset_ind: this.multiDefineList?.length + 1 || 1
    };

    // Tanımı listeye ekle
    if (!this.multiDefineList) {
      this.multiDefineList = [];
    }
    this.multiDefineList.push(newDefine);

    // Zamanları defineTimes listesine ekle
    if (!this.defineTimes) {
      this.defineTimes = [];
    }

    // Zamanları defineTimes listesine ekle
    const timesForDefinition = {
      ind: this.defineTimes.length + 1,
      sub: this.multiDefineTimes?.sub?.map((time: any) => ({
        ind: time.id,
        tr: time.timeTr,
        en: time.timeEn || time.timeTr // İngilizce tanım yoksa Türkçe kullan
      }))
    };

    // Zamanları defineTimes listesine ekle
    this.defineTimes.push(timesForDefinition);

    // Tanımları selectedVariables dizisine ekle (Gözden geçir adımında görünmesi için)
    this.updateSelectedVariablesForDefine();

    // Formu temizle
    this.clearDefine();
  }

  clearDefine(): void {
    this.multiDefine = {
      ind: this.multiDefineList?.length + 1 || 1,
      defineTr: null,
      defineEn: null,
      timeTable: [],
      inset_ind: null
    };

    // Zamanları sıfırla ama silme
    this.multiDefineTimes?.sub?.forEach((time: any) => {
      time.variable = null;
      time.variableId = null;
      time.timeTr = null;
      time.timeEn = null;
    });
  }

  removeDefine(define: any): void {
    if (!this.multiDefineList) return;

    const index = this.multiDefineList.indexOf(define);
    if (index !== -1) {
      this.multiDefineList.splice(index, 1);

      // Tanımları selectedVariables dizisinden kaldırıldığında güncelle
      this.updateSelectedVariablesForDefine();
    }
  }

  /**
   * Tanımları selectedVariables dizisine ekler (Gözden geçir adımında görünmesi için)
   */
  updateSelectedVariablesForDefine(): void {
    // Tanımlama adımının indeksini bul
    const config = this.ah.getConfiguration(this.currentAnalysisType);
    const defineStep = config.fields.find((f: any) => f.name === 'define_list');

    if (defineStep && defineStep.order) {
      // Tanımlama adımı için selectedVariables dizisini temizle
      this.selectedVariables[defineStep.order] = [];

      // Eğer tanımlar varsa, bunları selectedVariables dizisine ekle
      if (this.multiDefineList && this.multiDefineList.length > 0) {
        // Her tanım için bir temsili değişken oluştur
        this.multiDefineList.forEach((define, index) => {
          const defineVariable = {
            id: 1000000 + index, // Çakışmayı önlemek için büyük bir ID kullan
            name: define.defineTr,
            header: define.defineEn,
            measure: 'Scale', // Varsayılan ölçüm tipi
            selected: true,
            timeTable: define.timeTable, // Zaman tablosunu sakla
            isDefine: true // Bu bir tanım olduğunu belirt
          };

          // Temsili değişkeni selectedVariables dizisine ekle
          this.selectedVariables[defineStep.order].push(defineVariable);
        });
      }

      // Değişikliklerin algılanması için change detection'u tetikleyelim
      this.cdr.detectChanges();
    }
  }

  // Loading state
  isLoading: boolean = false;

  // Credit and report settings
  requiredCredits: number = 0;
  calculatingCredits: boolean = false;
  canPerformAnalysis: boolean = true; // Kullanıcının yeterli kredisi var mı?
  reportFormat: number = 1; // 1: ',' 2: '.'
  decimalPlaces: number = 2; // Ondalık basamak sayısı

  // Analysis info for each type - will be translated
  analysisInfo: { [key: string]: AnalysisInfo } = {
    descriptive: {
      title: '',
      description: '',
      helpText: '',
      examples: []
    },
    single: {
      title: '',
      description: '',
      helpText: '',
      examples: [],
      referenceValue: true
    },
    multi: {
      title: '',
      description: '',
      helpText: '',
      examples: []
    },
    dependent: {
      title: '',
      description: '',
      helpText: '',
      examples: []
    },
    correlation: {
      title: '',
      description: '',
      helpText: '',
      examples: []
    },
    chisq: {
      title: '',
      description: '',
      helpText: '',
      examples: []
    },
    comean: {
      title: '',
      description: '',
      helpText: '',
      examples: []
    },
    logistic_cox: {
      title: '',
      description: '',
      helpText: '',
      examples: []
    },
    survival: {
      title: '',
      description: '',
      helpText: '',
      examples: []
    },
    roc: {
      title: '',
      description: '',
      helpText: '',
      examples: []
    },
    linear: {
      title: '',
      description: '',
      helpText: '',
      examples: []
    }
  };

  // Translation keys for analysis info
  private analysisTranslationKeys = {
    descriptive: {
      title: 'analyses.descriptive.title',
      description: 'analyses.descriptive.description',
      helpText: 'analyses.descriptive.help_text',
      examples: [
        'analyses.descriptive.examples.1',
        'analyses.descriptive.examples.2',
        'analyses.descriptive.examples.3'
      ]
    },
    single: {
      title: 'analyses.single.title',
      description: 'analyses.single.description',
      helpText: 'analyses.single.help_text',
      examples: [
        'analyses.single.examples.1',
        'analyses.single.examples.2',
        'analyses.single.examples.3'
      ]
    },
    multi: {
      title: 'analyses.multi.title',
      description: 'analyses.multi.description',
      helpText: 'analyses.multi.help_text',
      examples: [
        'analyses.multi.examples.1',
        'analyses.multi.examples.2',
        'analyses.multi.examples.3'
      ]
    },
    dependent: {
      title: 'analyses.dependent.title',
      description: 'analyses.dependent.description',
      helpText: 'analyses.dependent.help_text',
      examples: [
        'analyses.dependent.examples.1',
        'analyses.dependent.examples.2',
        'analyses.dependent.examples.3'
      ]
    },
    correlation: {
      title: 'analyses.correlation.title',
      description: 'analyses.correlation.description',
      helpText: 'analyses.correlation.help_text',
      examples: [
        'analyses.correlation.examples.1',
        'analyses.correlation.examples.2',
        'analyses.correlation.examples.3'
      ]
    },
    chisq: {
      title: 'analyses.chisq.title',
      description: 'analyses.chisq.description',
      helpText: 'analyses.chisq.help_text',
      examples: [
        'analyses.chisq.examples.1',
        'analyses.chisq.examples.2',
        'analyses.chisq.examples.3'
      ]
    },
    comean: {
      title: 'analyses.comean.title',
      description: 'analyses.comean.description',
      helpText: 'analyses.comean.help_text',
      examples: [
        'analyses.comean.examples.1',
        'analyses.comean.examples.2',
        'analyses.comean.examples.3'
      ]
    },
    logistic_cox: {
      title: 'analyses.logistic_cox.title',
      description: 'analyses.logistic_cox.description',
      helpText: 'analyses.logistic_cox.help_text',
      examples: [
        'analyses.logistic_cox.examples.1',
        'analyses.logistic_cox.examples.2',
        'analyses.logistic_cox.examples.3'
      ]
    },
    survival: {
      title: 'analyses.survival.title',
      description: 'analyses.survival.description',
      helpText: 'analyses.survival.help_text',
      examples: [
        'analyses.survival.examples.1',
        'analyses.survival.examples.2',
        'analyses.survival.examples.3'
      ]
    },
    roc: {
      title: 'analyses.roc.title',
      description: 'analyses.roc.description',
      helpText: 'analyses.roc.help_text',
      examples: [
        'analyses.roc.examples.1',
        'analyses.roc.examples.2',
        'analyses.roc.examples.3'
      ]
    },
    linear: {
      title: 'analyses.linear.title',
      description: 'analyses.linear.description',
      helpText: 'analyses.linear.help_text',
      examples: [
        'analyses.linear.examples.1',
        'analyses.linear.examples.2',
        'analyses.linear.examples.3'
      ]
    }
  };

  constructor(
    private ah: AnalysisHelperService,
    public dialog: Dialog,
    @Inject(DIALOG_DATA) public data: any,
    public dialogRef: DialogRef<any>,
    private router: Router,
    private cdr: ChangeDetectorRef,
    private translocoService: TranslocoService,
    private snotifyService: SnotifyService,
    private datasetService: DatasetService,
    private creditUpdateService: CreditUpdateService
  ) { }

  ngOnInit(): void {

    // Get clone_variables from dialog data
    if (this.data && this.data.variables) {
      this.clone_variables = this.data.variables;
      this.isCloned = true;

      // If we have raid in data, use it as clonedReportId
      if (this.data.raid) {
        this.clonedReportId = this.data.raid;

      }
    }

    // Initialize the component with the selected analysis type
    // If we have clone_variables with analysis_group, use that as the analysis type
    if (this.clone_variables && this.clone_variables.analysis_group) {
      this.currentAnalysisType = this.clone_variables.analysis_group;
    } else if (this.data && this.data.selectedAnalyseType) {
      this.currentAnalysisType = this.data.selectedAnalyseType;
    }

    // If we have data from the parent component, use it
    if (this.data && this.data.aid && this.data.did && this.data.pid) {
      this.analysis_info = {
        aid: this.data.aid,
        did: this.data.did,
        pid: this.data.pid
      };
    }
    // If we have clone_variables with analysis_id in params, use that
    else if (this.clone_variables && this.clone_variables.params && this.clone_variables.params.analysis_id) {
      this.analysis_info = {
        aid: this.clone_variables.params.analysis_id,
        did: this.data.did, // We still need the dataset ID from data
        pid: this.data.pid  // We still need the project ID from data
      };

      // If clonedReportId is not set yet, use analysis_id from params
      if (!this.clonedReportId) {
        this.clonedReportId = this.clone_variables.params.analysis_id;

      }
    }
    // If we have clone_variables with analysis_id directly, use that
    else if (this.clone_variables && this.clone_variables.analysis_id) {
      this.analysis_info = {
        aid: this.clone_variables.analysis_id,
        did: this.data.did, // We still need the dataset ID from data
        pid: this.data.pid  // We still need the project ID from data
      };

      // If clonedReportId is not set yet, use analysis_id directly
      if (!this.clonedReportId) {
        this.clonedReportId = this.clone_variables.analysis_id;
      }
    }

    // Only load configuration if we have the necessary data
    if (this.analysis_info && this.analysis_info.did) {
      this.loadAnalysisConfiguration();
    } else {
      console.error('Missing required analysis information (did)');
    }

    // Subscribe to language changes
    this.translocoService.langChanges$.subscribe(() => {
      this.updateAnalysisInfoTranslations();
      this.updateWarningMessages();
    });

    // Initialize translations
    this.updateAnalysisInfoTranslations();

    // Başlangıçta tüm zamanlar için filtrelenmiş değişkenleri yükle
    setTimeout(() => {
      if (this.multiDefineTimes && this.multiDefineTimes.sub) {
        this.multiDefineTimes.sub.forEach((time: any) => {
          this.filterVariablesForTime(time);
        });
      }

      // Bağımlı analiz için tanımları selectedVariables dizisine ekle
      if (this.currentAnalysisType === 'dependent' && this.multiDefineList && this.multiDefineList.length > 0) {
        this.updateSelectedVariablesForDefine();
      }
    }, 500); // Verilerin yüklenmesi için biraz bekle
  }

  // Update analysis info translations
  private updateAnalysisInfoTranslations(): void {
    // Update translations for each analysis type
    Object.keys(this.analysisInfo).forEach(type => {
      this.analysisInfo[type].title = this.translocoService.translate(this.analysisTranslationKeys[type].title);
      this.analysisInfo[type].description = this.translocoService.translate(this.analysisTranslationKeys[type].description);
      this.analysisInfo[type].helpText = this.translocoService.translate(this.analysisTranslationKeys[type].helpText);

      // Update examples
      this.analysisInfo[type].examples = this.analysisTranslationKeys[type].examples.map(
        (key: string) => this.translocoService.translate(key)
      );
    });
  }

  // Update warning messages based on current language
  private updateWarningMessages(): void {
    // Reset any active warning to ensure it gets updated
    if (this.showStepWarning && this.warningMessage) {
      const currentWarning = this.warningMessage;
      this.warningMessage = this.translocoService.translate('create-analysis.warnings.' + currentWarning);
    }
  }

  loadAnalysisConfiguration(): void {
    if (!this.analysis_info || !this.analysis_info.did) {
      console.error('Cannot load analysis configuration: missing dataset ID');
      return;
    }

    this.isLoading = true;

    // Get the configuration for the current analysis type
    this.maxSteps = this.ah.getOrderCount(this.currentAnalysisType);
    this.reviewStep = this.maxSteps + 1;

    // Load variables from the dataset
    this.ah.getVariables(this.analysis_info.did).subscribe({
      next: (data) => {
        this.datasetVariables = data.variables.sort((a, b) => a.created_at - b.created_at)

        // Process variables based on analysis type
        // If clone_variables has a params property, use that instead
        let cloneParams = null;

        if (this.clone_variables) {

          // Check if clone_variables has a params property
          if (this.clone_variables.params) {
            cloneParams = this.clone_variables.params;
          } else {
            cloneParams = this.clone_variables;
          }
        }

        const processed = this.ah.processVariables(
          this.datasetVariables,
          this.currentAnalysisType,
          cloneParams
        );
        this.filteredVariables = processed.filteredVariables;
        if (processed.additional) {
          this.additionalOptions = processed.additional;
        }

        // Initialize selectedVariables based on the selected variables in filteredVariables
        if (this.clone_variables) {

          // For dependent analysis, handle the paired_list structure
          if (this.currentAnalysisType === 'dependent' && this.additionalOptions) {

            // Initialize times and insets
            if (this.additionalOptions.times && this.additionalOptions.times.length > 0) {
              this.defineTimes = [...this.additionalOptions.times];
            }

            if (this.additionalOptions.insets && this.additionalOptions.insets.length > 0) {
              // Convert insets to multiDefineList format
              this.multiDefineList = this.additionalOptions.insets.map((inset: any) => {
                // Find the corresponding times
                const times = this.defineTimes.find((t: any) => t.ind === inset.times_ind);

                // Create timeTable structure
                const timeTable = {
                  sub: []
                };

                // If we have times and variable_list
                if (times && times.sub && inset.variable_list) {
                  // Map each time to a variable
                  timeTable.sub = times.sub.map((time: any, index: number) => {
                    // Find the variable by ID
                    const variableId = inset.variable_list[index];
                    const variable = this.datasetVariables.find(v => v.id === variableId);

                    return {
                      id: time.ind,
                      timeTr: time.tr,
                      timeEn: time.en,
                      variable: variable,
                      variableId: variableId
                    };
                  });
                }

                return {
                  ind: inset.ind,
                  defineTr: inset.define.tr,
                  defineEn: inset.define.en,
                  timeTable: timeTable,
                  inset_ind: inset.times_ind
                };
              });

              // Update selectedVariables for define step
              this.updateSelectedVariablesForDefine();
            }

            // Handle split_list
            if (this.additionalOptions.split_list && this.additionalOptions.split_list.length > 0) {
              // Find the split_list step
              const config = this.ah.getConfiguration(this.currentAnalysisType);
              const splitStep = config.fields.find((f: any) => f.name === 'split_list');

              if (splitStep && splitStep.order) {
                // Initialize selectedVariables for split_list step
                if (!this.selectedVariables[splitStep.order]) {
                  this.selectedVariables[splitStep.order] = [];
                }

                // Add selected split variables
                this.additionalOptions.split_list.forEach((splitId: number) => {
                  const splitVar = this.datasetVariables.find(v => v.id === splitId);
                  if (splitVar) {
                    splitVar.selected = true;
                    this.selectedVariables[splitStep.order].push(splitVar);
                  }
                });
              }
            }
          } else {
            // For other analysis types, process normally
            Object.keys(this.filteredVariables).forEach(stepOrder => {
              const stepOrderNum = parseInt(stepOrder);
              if (!this.selectedVariables[stepOrderNum]) {
                this.selectedVariables[stepOrderNum] = [];
              }

              // Add selected variables to the selectedVariables array
              const selectedVars = this.filteredVariables[stepOrder].variable.filter((v: Variable) => v.selected);
              if (selectedVars.length > 0) {
                this.selectedVariables[stepOrderNum] = [...selectedVars];

                // For single analysis, update reference value field visibility
                if (this.currentAnalysisType === 'single' && stepOrderNum === 1) {
                  this.showReferenceValueFields = true;
                }

                // For chi-square analysis, update analysis type selection visibility
                if (this.currentAnalysisType === 'chisq' && stepOrderNum === 1) {
                  this.showChiSquareOptions = true;
                }

                // For mean comparison analysis, update time data question visibility
                if (this.currentAnalysisType === 'comean' && stepOrderNum === 1) {
                  this.showTimeDataQuestion = true;
                }
              }
            });
          }
        }

        // Check if we have analysis configuration in clone_variables
        if (this.clone_variables && this.clone_variables.params && this.clone_variables.params.analysis_configuration) {
          this.analysisConfiguration = this.clone_variables.params.analysis_configuration;


          // Initialize report settings from analysis configuration
          if (this.analysisConfiguration.separator) {
            this.reportFormat = this.analysisConfiguration.separator === ',' ? 1 : 2;
          }

          if (this.analysisConfiguration.precision !== undefined && this.analysisConfiguration.precision !== null) {
            this.decimalPlaces = this.analysisConfiguration.precision;
          }
        }

        // Check if we have time parameter in clone_variables for comean analysis
        if (this.currentAnalysisType === 'comean' && this.clone_variables && this.clone_variables.params) {
          // Set isInvolveTime based on the time parameter
          if (this.clone_variables.params.time !== undefined) {
            this.isInvolveTime = !!this.clone_variables.params.time;
            // Also show the time data question if we have factor_list variables
            if (this.clone_variables.params.factor_list && this.clone_variables.params.factor_list.length > 0) {
              this.showTimeDataQuestion = true;
            }
          }
        }
        // If not in clone_variables, load from dataset analyses
        else if (data.analyses && data.analyses.length > 0) {
          const analysis = data.analyses.find((a: any) => a.id === this.analysis_info.aid);
          if (analysis && analysis.analysis_configuration) {
            this.analysisConfiguration = analysis.analysis_configuration;

            // Initialize report settings from analysis configuration
            if (this.analysisConfiguration.separator) {
              this.reportFormat = this.analysisConfiguration.separator === ',' ? 1 : 2;
            }

            if (this.analysisConfiguration.precision !== undefined && this.analysisConfiguration.precision !== null) {
              this.decimalPlaces = this.analysisConfiguration.precision;
            }

          }
        }

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading variables:', error);
        this.isLoading = false;
      }
    });
  }

  // Navigation methods
  goToStep(step: number): void {
    // Mevcut adım için konfigürasyonu al
    const config = this.ah.getConfiguration(this.currentAnalysisType);
    const currentField = config.fields.find((f: any) => f.order === this.currentStep);

    // Hedef adımın önceki adımlarının tamamlanmış olmasını kontrol et
    if (step > this.currentStep) {
      // İsteğe bağlı adımlar için özel kontrol
      const isCurrentStepOptional = currentField && (currentField.name === 'split_list' || currentField.name === 'covariate_list');

      // Dependent analizi için yarım kalan tanımlama kontrolü
      if (this.currentAnalysisType === 'dependent' && this.currentStep === 1 && this.hasIncompleteDefine()) {
        // Uyarı modalını göster
        this.dialog.open(ConfirmComponent, {
          data: {
            title: this.translocoService.translate('analyses.dependent.incomplete_define.title'),
            content: this.translocoService.translate('analyses.dependent.incomplete_define.content'),
            confirm: this.translocoService.translate('analyses.dependent.incomplete_define.confirm'),
            cancel: this.translocoService.translate('analyses.dependent.incomplete_define.cancel')
          }
        }).closed.subscribe((result) => {
          if (result) {
            // Kullanıcı devam etmek istiyorsa, yarım kalan tanımı temizle ve adıma git
            this.clearDefine();
            this.goToStep(step);
          }
        });
        return;
      }

      // Korelasyon analizi için özel kontrol
      if (this.currentAnalysisType === 'correlation' && currentField && currentField.name === 'variable_list') {
        // Değişken seçimi adımında en az 2 değişken seçilmiş olmalı
        if (this.selectedVariables[this.currentStep] && this.selectedVariables[this.currentStep].length < 2) {
          // Uyarı göster
          this.warningMessage = this.translocoService.translate('analyses.correlation.min_variables_warning');
          this.showStepWarning = true;
          setTimeout(() => {
            this.showStepWarning = false;
            this.warningMessage = '';
          }, 5000); // 5 saniye sonra uyarıyı gizle
          return;
        }
      }

      // Mevcut adım tamamlanmadıysa ve isteğe bağlı değilse ilerleyemez
      if (!this.isStepComplete(this.currentStep) && !isCurrentStepOptional) {
        // Uyarı göster
        this.warningMessage = this.translocoService.translate('create-analysis.warnings.step_incomplete');
        this.showStepWarning = true;
        setTimeout(() => {
          this.showStepWarning = false;
          this.warningMessage = '';
        }, 5000); // 5 saniye sonra uyarıyı gizle
        return;
      }

      // Hedef adıma kadar olan tüm zorunlu adımların tamamlanmış olmasını kontrol et
      for (let i = 1; i < step; i++) {
        // Gözden geçir adımına geçiş için özel kontrol
        if (step === this.reviewStep) {
          // Gözden geçir adımına geçiş için tüm zorunlu adımların tamamlanmış olmasını kontrol et
          // Analiz tipine göre adım sayısını kontrol et
          const config = this.ah.getConfiguration(this.currentAnalysisType);
          const maxRequiredStep = config.fields.length;

          // Faktör seçimi ve bağımlı değişkenler adımlarını kontrol et
          if (this.currentAnalysisType === 'comean') {
            // Faktör seçimi adımından sonra bağımlı değişkenler adımı var mı?
            const factorField = config.fields.find((f: any) => f.name === 'factor_list');
            const dependentField = config.fields.find((f: any) => f.name === 'dependent_list');

            // Eğer her iki adım da varsa ve faktör seçimi adımındaysak ve bağımlı değişkenler adımında değişken seçilmediyse
            if (factorField && dependentField && this.currentStep === factorField.order && !this.isStepComplete(dependentField.order)) {
              // Bağımlı değişkenler adımını atlamaya çalışıyorsa uyarı göster
              this.warningMessage = this.translocoService.translate('create-analysis.warnings.dependent_first');
              this.showStepWarning = true;
              setTimeout(() => {
                this.showStepWarning = false;
                this.warningMessage = '';
              }, 5000); // 5 saniye sonra uyarıyı gizle
              return;
            }
          }

          // Diğer analizler için tüm zorunlu adımların tamamlanmış olmasını kontrol et
          for (let j = 1; j < maxRequiredStep; j++) {
            const stepField = config.fields.find((f: any) => f.order === j);
            const isStepOptional = stepField && (stepField.name === 'split_list' || stepField.name === 'covariate_list');

            // Zorunlu adımların tamamlanmış olmasını kontrol et
            if (!isStepOptional && !this.isStepComplete(j)) {
              // Uyarı göster
              this.warningMessage = this.translocoService.translate('create-analysis.warnings.all_steps');
              this.showStepWarning = true;
              setTimeout(() => {
                this.showStepWarning = false;
                this.warningMessage = '';
              }, 5000); // 5 saniye sonra uyarıyı gizle
              return;
            }
          }

          // Tüm kontroller geçildiyse gözden geçir adımına geçişe izin ver
          break;
        }

        // Adımın isteğe bağlı olup olmadığını kontrol et
        const stepField = config.fields.find((f: any) => f.order === i);
        const isStepOptional = stepField && (stepField.name === 'split_list' || stepField.name === 'covariate_list');

        // Zorunlu adımların tamamlanmış olmasını kontrol et
        if (!isStepOptional && !this.isStepComplete(i) && i !== this.currentStep) {
          // Uyarı göster
          this.warningMessage = this.translocoService.translate('create-analysis.warnings.previous_steps');
          this.showStepWarning = true;
          setTimeout(() => {
            this.showStepWarning = false;
            this.warningMessage = '';
          }, 5000); // 5 saniye sonra uyarıyı gizle
          return;
        }
      }
    }

    // Tek grup analizi için referans değeri kontrolü
    if (step > this.currentStep && this.currentAnalysisType === 'single') {
      // Mevcut adım için konfigürasyonu al
      const config = this.ah.getConfiguration(this.currentAnalysisType);
      const field = config.fields.find((f: any) => f.order === this.currentStep);

      // Eğer bu adım variable_list (Değişken Seçimi) ise ve seçilen değişkenler varsa
      if (field && field.name === 'variable_list' &&
        this.selectedVariables[this.currentStep] &&
        this.selectedVariables[this.currentStep].length > 0) {

        // Seçilen tüm değişkenlerin referans değeri girilmiş mi kontrol et
        const allHaveReferenceValues = this.selectedVariables[this.currentStep].every((variable: Variable) =>
          variable.referenceValue !== undefined &&
          variable.referenceValue !== null &&
          variable.referenceValue !== ''
        );

        // Referans değeri eksik varsa uyarı göster ve geçişe izin verme
        if (!allHaveReferenceValues) {
          this.warningMessage = this.translocoService.translate('create-analysis.warnings.reference_required');
          this.showStepWarning = true;
          setTimeout(() => {
            this.showStepWarning = false;
            this.warningMessage = '';
          }, 5000); // 5 saniye sonra uyarıyı gizle
          return;
        }

        // Validate that all reference values are valid numbers
        const allValidReferenceValues = this.selectedVariables[this.currentStep].every((variable: Variable) => {
          // Replace comma with dot for validation
          const normalizedValue = variable.referenceValue.replace(',', '.');
          // Check if it's a valid number
          return !isNaN(Number(normalizedValue));
        });

        // If any reference value is not a valid number, show warning and prevent proceeding
        if (!allValidReferenceValues) {
          this.warningMessage = this.translocoService.translate('create-analysis.warnings.invalid_reference_value');
          this.showStepWarning = true;
          setTimeout(() => {
            this.showStepWarning = false;
            this.warningMessage = '';
          }, 5000); // 5 saniye sonra uyarıyı gizle
          return;
        }
      }
    }

    // Geçerli adım aralığında ise adıma git
    if (step >= 1 && step <= this.reviewStep) {
      // Önceki adımı kaydet
      const previousStep = this.currentStep;

      // Yeni adıma geç
      this.currentStep = step;

      // Eğer gözden geçir adımına geçiliyorsa
      if (step === this.reviewStep && previousStep !== this.reviewStep) {
        // Bağımlı analiz için tanımları selectedVariables dizisine ekle
        if (this.currentAnalysisType === 'dependent' && this.multiDefineList && this.multiDefineList.length > 0) {
          this.updateSelectedVariablesForDefine();
        }

        // Kredi hesapla
        this.calculateRequiredCredits();
      }
    }
  }

  nextStep(): void {
    // Dependent analizi için yarım kalan tanımlama kontrolü
    if (this.currentAnalysisType === 'dependent' && this.currentStep === 1 && this.hasIncompleteDefine()) {
      // Uyarı modalını göster
      this.dialog.open(ConfirmComponent, {
        data: {
          title: this.translocoService.translate('analyses.dependent.incomplete_define.title'),
          content: this.translocoService.translate('analyses.dependent.incomplete_define.content'),
          confirm: this.translocoService.translate('analyses.dependent.incomplete_define.confirm'),
          cancel: this.translocoService.translate('analyses.dependent.incomplete_define.cancel')
        }
      }).closed.subscribe((result) => {
        if (result) {
          // Kullanıcı devam etmek istiyorsa, yarım kalan tanımı temizle ve sonraki adıma git
          this.clearDefine();
          this.nextStep();
        }
      });
      return;
    }

    // Korelasyon analizi için özel kontrol
    if (this.currentAnalysisType === 'correlation') {
      const config = this.ah.getConfiguration(this.currentAnalysisType);
      const currentField = config.fields.find((f: any) => f.order === this.currentStep);

      // Değişken seçimi adımında en az 2 değişken seçilmiş olmalı
      if (currentField && currentField.name === 'variable_list') {
        if (this.selectedVariables[this.currentStep] && this.selectedVariables[this.currentStep].length < 2) {
          // Uyarı göster
          this.warningMessage = this.translocoService.translate('analyses.correlation.min_variables_warning');
          this.showStepWarning = true;
          setTimeout(() => {
            this.showStepWarning = false;
            this.warningMessage = '';
          }, 5000); // 5 saniye sonra uyarıyı gizle
          return;
        }
      }
    }

    if (this.currentStep < this.reviewStep) {
      this.currentStep++;

      // Eğer gözden geçir adımına geçiliyorsa
      if (this.currentStep === this.reviewStep) {
        // Bağımlı analiz için tanımları selectedVariables dizisine ekle
        if (this.currentAnalysisType === 'dependent' && this.multiDefineList && this.multiDefineList.length > 0) {
          this.updateSelectedVariablesForDefine();
        }

        // Kredi hesapla
        this.calculateRequiredCredits();
      }
    }
  }

  previousStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  // Variable selection methods
  toggleVariableSelection(variable: Variable, stepOrder: number): void {
    // Don't allow selection of disabled variables
    if (variable.disabled) {
      return;
    }

    // Check if this is a split_list step and limit to 1 selection
    const config = this.ah.getConfiguration(this.currentAnalysisType);
    const field = config.fields.find((f: any) => f.order === stepOrder);
    const isSplitList = field && field.name === 'split_list';

    // Check if this is a step that should be limited to 1 selection for the new analysis types
    const isLimitedToOneSelection = isSplitList ||
      // For logistic_cox analysis
      (this.currentAnalysisType === 'logistic_cox' && field &&
        (field.name === 'status_list' || field.name === 'time_list' || field.name === 'strata_list')) ||
      // For survival analysis - TÜM ADIMLAR TEK SEÇİM
      (this.currentAnalysisType === 'survival' && field &&
        (field.name === 'status_list' || field.name === 'factor_list' || field.name === 'time_list' || field.name === 'strata_list')) ||
      // For roc analysis
      (this.currentAnalysisType === 'roc' && field &&
        (field.name === 'dependent_list' || field.name === 'split_list')) ||
      // For linear analysis - SADECE split_list tek seçim
      (this.currentAnalysisType === 'linear' && field &&
        (field.name === 'split_list')); // dependent_list kaldırıldı

    // If this is a step limited to 1 selection and we're trying to select a new variable when one is already selected
    if (isLimitedToOneSelection && !variable.selected && this.selectedVariables[stepOrder] && this.selectedVariables[stepOrder].length >= 1) {
      // Deselect the currently selected variable first
      const currentlySelected = this.selectedVariables[stepOrder][0];
      if (currentlySelected) {
        // Find and deselect in the filtered variables list
        if (this.filteredVariables[stepOrder] && this.filteredVariables[stepOrder].variable) {
          const selectedInList = this.filteredVariables[stepOrder].variable.find((v: Variable) => v.id === currentlySelected.id);
          if (selectedInList) {
            selectedInList.selected = false;
          }
        }
      }
      // Clear the selected variables array
      this.selectedVariables[stepOrder] = [];
    }

    variable.selected = !variable.selected;

    // Update selected variables
    if (!this.selectedVariables[stepOrder]) {
      this.selectedVariables[stepOrder] = [];
    }

    if (variable.selected) {
      // Add to selected variables for this step
      this.selectedVariables[stepOrder].push(variable);

      // Mark the variable as selected in the filtered variables list
      if (this.filteredVariables[stepOrder] && this.filteredVariables[stepOrder].variable) {
        const variableInList = this.filteredVariables[stepOrder].variable.find((v: Variable) => v.id === variable.id);
        if (variableInList) {
          variableInList.selected = true;
        }
      }
    } else {
      // Remove from selected variables for this step
      this.selectedVariables[stepOrder] = this.selectedVariables[stepOrder].filter(
        (v: Variable) => v.id !== variable.id
      );

      // Mark the variable as not selected in the filtered variables list
      if (this.filteredVariables[stepOrder] && this.filteredVariables[stepOrder].variable) {
        const variableInList = this.filteredVariables[stepOrder].variable.find((v: Variable) => v.id === variable.id);
        if (variableInList) {
          variableInList.selected = false;
        }
      }
    }

    // For single analysis, update reference value field visibility
    if (this.currentAnalysisType === 'single' && stepOrder === 1) {
      this.showReferenceValueFields = this.selectedVariables[stepOrder] && this.selectedVariables[stepOrder].length > 0;

      // If a variable was just selected
      if (variable.selected) {
        // Check if there's a previously selected variable with a reference value
        const previousVariables = this.selectedVariables[stepOrder].filter((v: Variable) => v.id !== variable.id && v.referenceValue);

        // If there's a previously selected variable with a reference value, use that value for the new variable
        if (previousVariables.length > 0) {
          // Get the most recently added variable with a reference value
          const lastVariable = previousVariables[previousVariables.length - 1];
          // Set the reference value for the newly selected variable
          variable.referenceValue = lastVariable.referenceValue;
        }

        // Use setTimeout to ensure the DOM has been updated
        setTimeout(() => {
          const inputElement = document.getElementById(`reference-input-${variable.id}`);
          if (inputElement) {
            inputElement.focus();
          }
        }, 0);
      }
    }

    // For chi-square analysis, update analysis type selection visibility
    if (this.currentAnalysisType === 'chisq' && stepOrder === 1) {
      this.showChiSquareOptions = this.selectedVariables[stepOrder] && this.selectedVariables[stepOrder].length > 0;
    }

    // For mean comparison analysis, update time data question visibility
    if (this.currentAnalysisType === 'comean' && stepOrder === 1) {
      this.showTimeDataQuestion = this.selectedVariables[stepOrder] && this.selectedVariables[stepOrder].length === 1;
    }
  }

  updateReferenceValue(variable: Variable, value: string): void {
    // Get the current language
    const currentLang = this.translocoService.getActiveLang();

    // If the value is empty, just set it
    if (!value) {
      variable.referenceValue = value;
      return;
    }

    // Replace any invalid characters based on the current language
    if (currentLang === 'tr') {
      // For Turkish, allow only numbers and comma as decimal separator
      // Replace any dots with commas
      value = value.replace(/\./g, ',');
      // Remove any non-numeric characters except comma
      value = value.replace(/[^0-9,]/g, '');
      // Ensure only one comma
      const parts = value.split(',');
      if (parts.length > 2) {
        value = parts[0] + ',' + parts.slice(1).join('');
      }
    } else {
      // For other languages (English), allow only numbers and dot as decimal separator
      // Replace any commas with dots
      value = value.replace(/,/g, '.');
      // Remove any non-numeric characters except dot
      value = value.replace(/[^0-9\.]/g, '');
      // Ensure only one dot
      const parts = value.split('.');
      if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
      }
    }

    variable.referenceValue = value;
  }

  // Handle keypress events for reference value input
  handleReferenceValueKeypress(event: KeyboardEvent, variable: Variable): boolean {
    const currentLang = this.translocoService.getActiveLang();
    const key = event.key;

    // Allow control keys (backspace, delete, etc.)
    if (event.key === 'Backspace' || event.key === 'Delete' || event.key === 'ArrowLeft' ||
      event.key === 'ArrowRight' || event.key === 'Tab' || event.key === 'Enter' ||
      event.ctrlKey || event.altKey || event.metaKey) {
      return true;
    }

    if (currentLang === 'tr') {
      // For Turkish, allow only numbers and comma
      if (!/[0-9,]/.test(key)) {
        event.preventDefault();
        return false;
      }

      // Allow only one comma
      if (key === ',' && variable.referenceValue && variable.referenceValue.includes(',')) {
        event.preventDefault();
        return false;
      }
    } else {
      // For other languages (English), allow only numbers and dot
      if (!/[0-9\.]/.test(key)) {
        event.preventDefault();
        return false;
      }

      // Allow only one dot
      if (key === '.' && variable.referenceValue && variable.referenceValue.includes('.')) {
        event.preventDefault();
        return false;
      }
    }

    return true;
  }

  // Format reference value based on current language
  formatReferenceValue(value: string): string {
    if (!value) return '';

    const currentLang = this.translocoService.getActiveLang();

    if (currentLang === 'tr') {
      // For Turkish, ensure comma as decimal separator
      return value.includes(',') ? value : value.replace('.', ',');
    } else {
      // For other languages (English), ensure dot as decimal separator
      return value.includes('.') ? value : value.replace(',', '.');
    }
  }

  /**
   * Gets the formatted preview number based on current settings
   */
  getFormattedPreviewNumber(): string {
    const separator = this.reportFormat === 1 ? ',' : '.';
    return 1234.5678.toFixed(this.decimalPlaces).replace('.', separator);
  }

  // Filter methods
  toggleMeasureFilter(measure: string): void {
    this.measureFilters[measure] = !this.measureFilters[measure];
  }

  // Belirli bir ölçüm tipinde değişken olup olmadığını kontrol eder
  hasMeasureTypeVariables(measure: string, stepOrder: number): boolean {
    if (!this.filteredVariables[stepOrder] || !this.filteredVariables[stepOrder].variable) {
      return false;
    }

    return this.filteredVariables[stepOrder].variable.some((v: Variable) => v.measure === measure);
  }

  filterVariables(variables: Variable[]): Variable[] {
    // Değişkenleri disabled olarak işaretle
    variables.forEach(v => {
      // Disabled nedenini sıfırla
      v.disabledReason = undefined;

      // Korelasyon analizi için özel kontrol
      if (this.currentAnalysisType === 'correlation') {
        const config = this.ah.getConfiguration(this.currentAnalysisType);
        const currentField = config.fields.find((f: any) => f.order === this.currentStep);

        // row_list veya column_list adımındaysak, diğer değişkenleri disabled yapma
        if (currentField && (currentField.name === 'row_list' || currentField.name === 'column_list')) {
          // Sadece split_list adımında seçili olup olmadığını kontrol et
          if (this.isVariableSelectedInOtherStep(v.id, this.currentStep)) {
            v.disabled = true;
            v.disabledReason = 'in_use';
          } else {
            v.disabled = false;
          }
        } else {
          // split_list adımında normal kontroller yapılır
          if (this.isVariableSelectedInOtherStep(v.id, this.currentStep)) {
            v.disabled = true;
            v.disabledReason = 'in_use';
          } else {
            v.disabled = false;
          }
        }
      } else {
        // Diğer analiz tipleri için normal kontrol
        if (this.isVariableSelectedInOtherStep(v.id, this.currentStep)) {
          v.disabled = true;
          v.disabledReason = 'in_use';
        } else {
          v.disabled = false;
        }
      }

      // Tek grup analizi için Ordinal değişkenleri variable_list adımında devre dışı bırak
      if (this.currentAnalysisType === 'single') {
        const config = this.ah.getConfiguration(this.currentAnalysisType);
        const field = config.fields.find((f: any) => f.order === this.currentStep);

        if (field && field.name === 'variable_list' && v.measure === 'Ordinal') {
          v.disabled = true;
          v.disabledReason = 'ordinal_not_allowed';
        }
      }

      // Ki-kare analizi için 2'den az etiketli değişkenleri devre dışı bırak
      if (this.currentAnalysisType == 'chisq') {
        const config = this.ah.getConfiguration(this.currentAnalysisType);
        const field = config.fields.find((f: any) => f.order === this.currentStep);

        // Nominal veya Ordinal değişkenler için etiket sayısı kontrolü yap
        if (field && field.name !== 'split_list' &&
          (v.measure === 'Nominal' || v.measure === 'Ordinal') &&
          !this.hasEnoughValueLabels(v)) {
          v.disabled = true;
          v.disabledReason = 'not_enough_labels';
        }
      }

      // For logistic_cox analysis, apply specific filters
      if (this.currentAnalysisType === 'logistic_cox') {
        const config = this.ah.getConfiguration(this.currentAnalysisType);
        const field = config.fields.find((f: any) => f.order === this.currentStep);

        // For status_dependent_list, only allow Nominal/Ordinal variables with exactly 2 value labels
        if (field && field.name === 'status_dependent_list' &&
          (v.measure !== 'Nominal' && v.measure !== 'Ordinal')) {
          v.disabled = true;
          v.disabledReason = 'wrong_measure_type';
        } else if (field && field.name === 'status_dependent_list' &&
          (v.measure === 'Nominal' || v.measure === 'Ordinal')) {
          // Check if it has exactly 2 value labels
          const valueLabels = this.getValueLabels(v);
          if (!valueLabels || valueLabels.length !== 2) {
            v.disabled = true;
            v.disabledReason = 'needs_exactly_two_labels';
          }
        }

        // For time_list, only allow Scale variables
        if (field && field.name === 'time_list' && v.measure !== 'Scale') {
          v.disabled = true;
          v.disabledReason = 'wrong_measure_type';
        }
      }

      // For survival analysis, apply specific filters
      if (this.currentAnalysisType === 'survival') {
        const config = this.ah.getConfiguration(this.currentAnalysisType);
        const field = config.fields.find((f: any) => f.order === this.currentStep);

        // For status_list, only allow Nominal/Ordinal variables with exactly 2 value labels
        if (field && field.name === 'status_list' &&
          (v.measure !== 'Nominal' && v.measure !== 'Ordinal')) {
          v.disabled = true;
          v.disabledReason = 'wrong_measure_type';
        } else if (field && field.name === 'status_list' &&
          (v.measure === 'Nominal' || v.measure === 'Ordinal')) {
          // Check if it has exactly 2 value labels
          const valueLabels = this.getValueLabels(v);
          if (!valueLabels || valueLabels.length !== 2) {
            v.disabled = true;
            v.disabledReason = 'needs_exactly_two_labels';
          }
        }

        // For time_list, only allow Scale variables
        if (field && field.name === 'time_list' && v.measure !== 'Scale') {
          v.disabled = true;
          v.disabledReason = 'wrong_measure_type';
        }

        // For factor_list, only allow Nominal/Ordinal variables with at least 2 value labels
        if (field && field.name === 'factor_list' &&
          (v.measure !== 'Nominal' && v.measure !== 'Ordinal')) {
          v.disabled = true;
          v.disabledReason = 'wrong_measure_type';
        } else if (field && field.name === 'factor_list' &&
          (v.measure === 'Nominal' || v.measure === 'Ordinal') &&
          !this.hasEnoughValueLabels(v)) {
          v.disabled = true;
          v.disabledReason = 'not_enough_labels';
        }
      }

      // For ROC analysis, apply specific filters
      if (this.currentAnalysisType === 'roc') {
        const config = this.ah.getConfiguration(this.currentAnalysisType);
        const field = config.fields.find((f: any) => f.order === this.currentStep);

        // For dependent_list, only allow Nominal/Ordinal variables with exactly 2 value labels
        if (field && field.name === 'dependent_list' &&
          (v.measure !== 'Nominal' && v.measure !== 'Ordinal')) {
          v.disabled = true;
          v.disabledReason = 'wrong_measure_type';
        } else if (field && field.name === 'dependent_list' &&
          (v.measure === 'Nominal' || v.measure === 'Ordinal')) {
          // Check if it has exactly 2 value labels
          const valueLabels = this.getValueLabels(v);
          if (!valueLabels || valueLabels.length !== 2) {
            v.disabled = true;
            v.disabledReason = 'needs_exactly_two_labels';
          }
        }
      }

      // For Linear analysis, apply specific filters
      if (this.currentAnalysisType === 'linear') {
        const config = this.ah.getConfiguration(this.currentAnalysisType);
        const field = config.fields.find((f: any) => f.order === this.currentStep);

        // For dependent_list, only allow Scale variables
        if (field && field.name === 'dependent_list' && v.measure !== 'Scale') {
          v.disabled = true;
          v.disabledReason = 'wrong_measure_type';
        }

        // For independent_list, allow all variable types
        // No specific restrictions needed
      }
    });

    // First, get the IDs of selected variables for the current step
    const selectedIds = this.selectedVariables[this.currentStep] ?
      this.selectedVariables[this.currentStep].map((v: Variable) => v.id) : [];

    // Seçili değişkenlerin durumunu güncelle
    if (selectedIds.length > 0) {
      variables.forEach(v => {
        if (selectedIds.includes(v.id)) {
          v.selected = true;
        }
      });
    }

    // Filtrele
    return variables.filter(v => {
      // Seçili değişkenleri her zaman dahil et
      if (v.selected) {
        return true;
      }

      // Ölçüm türüne göre filtrele
      if (!this.measureFilters[v.measure]) {
        return false;
      }

      // Arama terimine göre filtrele
      if (this.searchTerm && this.searchTerm.trim() !== '') {
        const term = this.searchTerm.toLocaleLowerCase('tr-TR');
        return v.name.toLocaleLowerCase('tr-TR').includes(term) ||
          (v.description && v.description.toLocaleLowerCase('tr-TR').includes(term));
      }

      return true;
    });
  }

  // Helper method to check if a variable is selected in another step
  isVariableSelectedInOtherStep(variableId: number, currentStep: number): boolean {
    // Korelasyon analizi için özel kontrol
    if (this.currentAnalysisType === 'correlation') {
      const config = this.ah.getConfiguration(this.currentAnalysisType);
      const currentField = config.fields.find((f: any) => f.order === currentStep);

      // Eğer şu anki adım row_list veya column_list ise, sadece split_list'te kullanılıp kullanılmadığını kontrol et
      if (currentField && (currentField.name === 'row_list' || currentField.name === 'column_list')) {
        // Sadece split_list adımında seçili olup olmadığını kontrol et
        const splitListStep = config.fields.find((f: any) => f.name === 'split_list')?.order;
        if (splitListStep && this.selectedVariables[splitListStep] && this.selectedVariables[splitListStep].length > 0) {
          return this.selectedVariables[splitListStep].some((v: Variable) => v.id === variableId);
        }

        return false; // split_list'te seçili değilse, seçilebilir
      }

      // Eğer şu anki adım split_list ise, diğer adımlarda seçili olup olmadığını kontrol et
      if (currentField && currentField.name === 'split_list') {
        // row_list ve column_list adımlarında seçili olup olmadığını kontrol et
        for (let step = 1; step <= this.maxSteps; step++) {
          if (step === currentStep) {
            continue;
          }

          if (this.selectedVariables[step] && this.selectedVariables[step].length > 0) {
            if (this.selectedVariables[step].some((v: Variable) => v.id === variableId)) {
              return true;
            }
          }
        }
      }
    } else {
      // Diğer analizler için mevcut kontrol
      for (let step = 1; step <= this.maxSteps; step++) {
        if (step === currentStep) {
          continue;
        }

        if (this.selectedVariables[step] && this.selectedVariables[step].length > 0) {
          if (this.selectedVariables[step].some((v: Variable) => v.id === variableId)) {
            return true;
          }
        }
      }
    }

    return false;
  }

  // Submit analysis
  submitAnalysis(): void {
    if (!this.analysis_info || !this.analysis_info.aid) {
      console.error('Cannot submit analysis: missing analysis ID');
      return;
    }

    // Eğer klonlanmış bir rapor ise, kullanıcıya seçenek sun
    if (this.isCloned && this.clonedReportId) {
      this.showCloneOptions();
      return;
    }

    // Normal analiz gönderme işlemine devam et
    this.processAnalysisSubmission();
  }

  // Klonlama seçeneklerini göster
  showCloneOptions(): void {

    this.showCloneOptionsModal = true;
  }

  // Klonlama seçeneğini işle
  handleCloneOption(createNew: boolean): void {

    this.showCloneOptionsModal = false;

    if (createNew) {
      // Yeni rapor oluştur
      this.processAnalysisSubmission();
    } else {
      // Mevcut raporu güncelle

      this.updateExistingReport();
    }
  }

  // Klonlama seçeneklerini iptal et
  cancelCloneOptions(): void {
    this.showCloneOptionsModal = false;
  }

  // Mevcut raporu güncelle
  updateExistingReport(): void {
    if (!this.clonedReportId) {
      console.error('Cannot update report: missing report ID');
      return;
    }

    this.isLoading = true;

    // Prepare variables for submission
    let variables = {};
    let additionalOptions = null;

    // Dependent analizi için özel işlem
    if (this.currentAnalysisType === 'dependent') {
      variables = {
        defineTimes: this.defineTimes || [],
        multiDefineList: this.multiDefineList || [],
        splitList: this.selectedVariables[2] || []
      };
    } else {
      // Diğer analizler için normal işlem
      Object.keys(this.filteredVariables).forEach(key => {
        // Filteredvariables'ı kopyala
        const filteredVarsCopy = { ...this.filteredVariables[key] };

        // Eğer bu adım için seçilen değişkenler varsa, sıralamayı güncelle
        if (this.selectedVariables[key] && this.selectedVariables[key].length > 0) {
          // Seçilen değişkenlerin ID'lerini al
          const selectedIds = this.selectedVariables[key].map((v: Variable) => v.id);

          // Filtrelenmiş değişkenleri seçilen değişkenlerin sırasına göre sırala
          filteredVarsCopy.variable = filteredVarsCopy.variable.sort((a: Variable, b: Variable) => {
            // Eğer her ikisi de seçili değilse, orijinal sıralamayı koru
            if (!a.selected && !b.selected) return 0;

            // Eğer sadece a seçili ise, a'ı öne al
            if (a.selected && !b.selected) return -1;

            // Eğer sadece b seçili ise, b'yi öne al
            if (!a.selected && b.selected) return 1;

            // Her ikisi de seçili ise, selectedVariables'daki sıralamaya göre sırala
            return selectedIds.indexOf(a.id) - selectedIds.indexOf(b.id);
          });
        }

        variables[key] = filteredVarsCopy;
      });
    }

    // Ki-kare analizi için rapor türü seçimini ekle
    if (this.currentAnalysisType === 'chisq') {
      additionalOptions = this.selectedReportType;
    }

    // Ortalama karşılaştırma analizi için zaman içerip içermediği bilgisini ekle
    if (this.currentAnalysisType === 'comean') {
      additionalOptions = this.isInvolveTime;
    }

    // Yeni analiz tipleri için özel işlemler
    if (['logistic_cox', 'survival', 'roc', 'linear'].includes(this.currentAnalysisType)) {
      // Şu an için ek seçenekler yok, ileride eklenebilir
    }

    try {
      // Send clone analysis request with simplified analysis configuration
      const options = {
        additionalOptions,
        analysisConfiguration: {
          precision: this.decimalPlaces,
          separator: this.reportFormat === 1 ? ',' : '.'
        }
      };

      const cloneRequest = this.ah.sendCloneAnalysisRequest(
        this.currentAnalysisType,
        this.analysis_info.aid,
        variables,
        this.clonedReportId,
        options
      );

      if (cloneRequest) {
        cloneRequest.subscribe({
          next: (response) => {
            this.isLoading = false;

            // Analiz tamamlandığında, ilgili rapor detayı sayfasına yönlendir
            if (response && response.reports && response.reports.length > 0 && response.reports[0].id) {
              const reportId = response.reports[0].id;

              // Notify the CreditUpdateService that credits have been updated
              this.creditUpdateService.notifyCreditUpdated();

              // Rapor detayı sayfasına yönlendir
              this.router.navigate(['/reports', reportId]);

              // Dialog'u kapat
              this.dialogRef.close(true);
            } else {
              console.error('Invalid response format or missing report ID');
            }
          },
          error: (error) => {
            console.error('Error updating report:', error);
            this.isLoading = false;

            // Show error notification to user
            let errorMessage = this.translocoService.translate('notification.analysis.submit.error.message');
            if (error.error && error.error.message) {
              errorMessage = error.error.message;
            } else if (error.message) {
              errorMessage = error.message;
            }

            this.snotifyService.error(
              errorMessage,
              this.translocoService.translate('notification.analysis.submit.error.title'),
              {
                timeout: 3000,
                showProgressBar: true,
                closeOnClick: true,
                pauseOnHover: true,
                position: 'centerBottom'
              }
            );
          }
        });
      } else {
        console.error('Failed to create update request');
        this.isLoading = false;

        // Show error notification to user
        this.snotifyService.error(
          this.translocoService.translate('notification.analysis.submit.request_failed.message'),
          this.translocoService.translate('notification.analysis.submit.request_failed.title'),
          {
            timeout: 3000,
            showProgressBar: true,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );
      }
    } catch (error) {
      console.error('Error updating report:', error);
      this.isLoading = false;

      // Show error notification to user
      let errorMessage = this.translocoService.translate('notification.analysis.general.error.message');
      if (error.message) {
        errorMessage = error.message;
      }

      this.snotifyService.error(
        errorMessage,
        this.translocoService.translate('notification.analysis.general.error.title'),
        {
          timeout: 3000,
          showProgressBar: true,
          closeOnClick: true,
          pauseOnHover: true,
          position: 'centerBottom'
        }
      );
    }
  }

  // Normal analiz gönderme işlemi
  processAnalysisSubmission(): void {
    this.isLoading = true;

    // Prepare variables for submission
    let variables = {};
    let additionalOptions = null;

    // Dependent analizi için özel işlem
    if (this.currentAnalysisType === 'dependent') {
      variables = {
        defineTimes: this.defineTimes || [],
        multiDefineList: this.multiDefineList || [],
        splitList: this.selectedVariables[2] || []
      };
    } else {
      // Diğer analizler için normal işlem
      Object.keys(this.filteredVariables).forEach(key => {
        // Filteredvariables'ı kopyala
        const filteredVarsCopy = { ...this.filteredVariables[key] };

        // Eğer bu adım için seçilen değişkenler varsa, sıralamayı güncelle
        if (this.selectedVariables[key] && this.selectedVariables[key].length > 0) {
          // Seçilen değişkenlerin ID'lerini al
          const selectedIds = this.selectedVariables[key].map((v: Variable) => v.id);

          // Filtrelenmiş değişkenleri seçilen değişkenlerin sırasına göre sırala
          filteredVarsCopy.variable = filteredVarsCopy.variable.sort((a: Variable, b: Variable) => {
            // Eğer her ikisi de seçili değilse, orijinal sıralamayı koru
            if (!a.selected && !b.selected) return 0;

            // Eğer sadece a seçili ise, a'ı öne al
            if (a.selected && !b.selected) return -1;

            // Eğer sadece b seçili ise, b'yi öne al
            if (!a.selected && b.selected) return 1;

            // Her ikisi de seçili ise, selectedVariables'daki sıralamaya göre sırala
            return selectedIds.indexOf(a.id) - selectedIds.indexOf(b.id);
          });
        }

        variables[key] = filteredVarsCopy;
      });
    }

    // Ki-kare analizi için rapor türü seçimini ekle
    if (this.currentAnalysisType === 'chisq') {
      additionalOptions = this.selectedReportType;
    }

    // Ortalama karşılaştırma analizi için zaman içerip içermediği bilgisini ekle
    if (this.currentAnalysisType === 'comean') {
      additionalOptions = this.isInvolveTime;
    }

    // Yeni analiz tipleri için özel işlemler
    if (['logistic_cox', 'survival', 'roc', 'linear'].includes(this.currentAnalysisType)) {
      // Şu an için ek seçenekler yok, ileride eklenebilir
    }

    try {
      // Send analysis request with simplified analysis configuration
      const options = {
        additionalOptions,
        analysisConfiguration: {
          precision: this.decimalPlaces,
          separator: this.reportFormat === 1 ? ',' : '.'
        }
      };

      const analysisRequest = this.ah.sendAnalysisRequest(
        this.currentAnalysisType,
        this.analysis_info.aid,
        variables,
        options
      );

      if (analysisRequest) {
        analysisRequest.subscribe({
          next: (response) => {
            this.isLoading = false;

            // Analiz tamamlandığında, ilgili rapor detayı sayfasına yönlendir
            if (response && response.reports && response.reports.length > 0 && response.reports[0].id) {
              const reportId = response.reports[0].id;

              // Notify the CreditUpdateService that credits have been updated
              this.creditUpdateService.notifyCreditUpdated();

              // Rapor detayı sayfasına yönlendir
              this.router.navigate(['/reports', reportId]);

              // Dialog'u kapat
              this.dialogRef.close();
            } else {
              console.error('Invalid response format or missing report ID');
            }
          },
          error: (error) => {
            console.error('Error submitting analysis:', error);
            this.isLoading = false;

            // Show error notification to user
            let errorMessage = this.translocoService.translate('notification.analysis.submit.error.message');
            if (error.error && error.error.message) {
              errorMessage = error.error.message;
            } else if (error.message) {
              errorMessage = error.message;
            }

            this.snotifyService.error(
              errorMessage,
              this.translocoService.translate('notification.analysis.submit.error.title'),
              {
                timeout: 3000,
                showProgressBar: true,
                closeOnClick: true,
                pauseOnHover: true,
                position: 'centerBottom'
              }
            );
          }
        });
      } else {
        console.error('Failed to create analysis request');
        this.isLoading = false;

        // Show error notification to user
        this.snotifyService.error(
          this.translocoService.translate('notification.analysis.submit.request_failed.message'),
          this.translocoService.translate('notification.analysis.submit.request_failed.title'),
          {
            timeout: 3000,
            showProgressBar: true,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );
      }
    } catch (error) {
      console.error('Error creating analysis request:', error);
      this.isLoading = false;

      // Show error notification to user
      let errorMessage = this.translocoService.translate('notification.analysis.general.error.message');
      if (error.message) {
        errorMessage = error.message;
      }

      this.snotifyService.error(
        errorMessage,
        this.translocoService.translate('notification.analysis.general.error.title'),
        {
          timeout: 3000,
          showProgressBar: true,
          closeOnClick: true,
          pauseOnHover: true,
          position: 'centerBottom'
        }
      );
    }
  }

  // Helper methods
  getStepName(step: number): string {
    if (step === this.reviewStep) {
      return this.translocoService.translate('create-analysis.steps.review');
    }

    const config = this.ah.getConfiguration(this.currentAnalysisType);
    const field = config.fields.find((f: any) => f.order === step);

    if (field) {
      // Transloco ile çeviri yap
      const translationKey = `create-analysis.steps.${field.name}`;
      return this.translocoService.translate(translationKey);
    }

    return `${this.translocoService.translate('create-analysis.step_prefix')} ${step}`;
  }

  isStepComplete(step: number): boolean {
    if (step === this.reviewStep) {
      // Review step is complete if all previous steps are complete
      for (let i = 1; i < this.reviewStep; i++) {
        if (!this.isStepComplete(i)) {
          return false;
        }
      }
      return true;
    }

    // Dependent analizi için özel kontrol
    if (this.currentAnalysisType === 'dependent') {
      if (step === 1) { // Tanımlama adımı
        return this.multiDefineList?.length > 0 || false;
      } else if (step === 2) { // Ayırma değişkeni adımı
        // Ayırma değişkeni seçilmesi zorunlu değil
        return true;
      }
    }

    // Korelasyon analizi için özel kontrol
    if (this.currentAnalysisType === 'correlation') {
      const config = this.ah.getConfiguration(this.currentAnalysisType);
      const field = config.fields.find((f: any) => f.order === step);

      // For row_list and column_list, require at least one selection each
      if (field && (field.name === 'row_list' || field.name === 'column_list')) {
        return this.selectedVariables[step] && this.selectedVariables[step].length > 0;
      }
    }

    // Kontrol et: Bu adım split_list (Ayırma Değişkeni) veya covariate_list (Kovaryant Seçimi) mi?
    const config = this.ah.getConfiguration(this.currentAnalysisType);
    const field = config.fields.find((f: any) => f.order === step);

    // Eğer bu adım split_list (Ayırma Değişkeni) veya covariate_list (Kovaryant Seçimi) ise özel kontrol yap
    if (field && (field.name === 'split_list' || field.name === 'covariate_list')) {
      // Sadece iki durumda tamamlanmış say:
      // 1. Değişken seçilmişse
      // 2. Gözden geçir adımındaysak

      // Değişken seçilmiş mi kontrol et
      const hasSelectedVariables = this.selectedVariables[step] && this.selectedVariables[step].length > 0;

      // Gözden geçir adımında mıyız?
      const isInReviewStep = this.currentStep === this.reviewStep;

      // Sadece bu iki durumdan biri doğruysa tamamlanmış say
      return hasSelectedVariables || isInReviewStep;
    }

    // Diğer adımlar için, en az bir değişken seçilmiş olmalı
    return this.selectedVariables[step] && this.selectedVariables[step].length > 0;
  }

  canProceedToNextStep(): boolean {
    // Dependent analizi için özel kontrol
    if (this.currentAnalysisType === 'dependent') {
      if (this.currentStep === 1) { // Tanımlama adımı
        return this.multiDefineList?.length > 0 || false;
      } else if (this.currentStep === 2) { // Ayırma değişkeni adımı
        // Ayırma değişkeni seçilmesi zorunlu değil
        return true;
      }
    }

    // Korelasyon analizi için özel kontrol
    if (this.currentAnalysisType === 'correlation') {
      const config = this.ah.getConfiguration(this.currentAnalysisType);
      const field = config.fields.find((f: any) => f.order === this.currentStep);

      // For row_list and column_list, require at least one selection each
      if (field && (field.name === 'row_list' || field.name === 'column_list')) {
        return this.selectedVariables[this.currentStep] && this.selectedVariables[this.currentStep].length > 0;
      }
    }

    // Ayırma Değişkeni, Kovaryant Seçimi, Strata Listesi adımları için özel kontrol
    const config = this.ah.getConfiguration(this.currentAnalysisType);
    const field = config.fields.find((f: any) => f.order === this.currentStep);

    // Eğer bu adım split_list (Ayırma Değişkeni), covariate_list (Kovaryant Seçimi), strata_list (Strata Listesi) ise, değişken seçilmese de ilerlenebilir
    if (field && (field.name === 'split_list' || field.name === 'covariate_list' || field.name === 'strata_list')) {
      return true; // Bu adımlarda her zaman ilerlenebilir
    }

    // Tek grup analizi için referans değeri kontrolü
    if (this.currentAnalysisType === 'single' && field && field.name === 'variable_list') {
      // Önce temel kontrol: en az bir değişken seçilmiş mi?
      if (!this.selectedVariables[this.currentStep] || this.selectedVariables[this.currentStep].length === 0) {
        return false;
      }

      // Seçilen tüm değişkenlerin referans değeri girilmiş mi kontrol et
      const allHaveReferenceValues = this.selectedVariables[this.currentStep].every((variable: Variable) =>
        variable.referenceValue !== undefined &&
        variable.referenceValue !== null &&
        variable.referenceValue !== ''
      );

      if (!allHaveReferenceValues) {
        return false;
      }

      // Validate that all reference values are valid numbers
      return this.selectedVariables[this.currentStep].every((variable: Variable) => {
        // Replace comma with dot for validation
        const normalizedValue = variable.referenceValue.replace(',', '.');
        // Check if it's a valid number
        return !isNaN(Number(normalizedValue));
      });
    }

    // Logistic Cox analizi için özel kontrol
    if (this.currentAnalysisType === 'logistic_cox') {
      // Sadece status_dependent_list adımı zorunlu, time_list adımı isteğe bağlı
      if (field && field.name === 'status_dependent_list') {
        return this.selectedVariables[this.currentStep] && this.selectedVariables[this.currentStep].length > 0;
      }
      // time_list adımı için her zaman true döndür (zorunlu değil)
      if (field && field.name === 'time_list') {
        return true;
      }
    }

    // Survival analizi için özel kontrol
    if (this.currentAnalysisType === 'survival') {
      // Status, factor ve time değişkenleri zorunlu
      if (field && (field.name === 'status_list' || field.name === 'factor_list' || field.name === 'time_list')) {
        return this.selectedVariables[this.currentStep] && this.selectedVariables[this.currentStep].length > 0;
      }
    }

    // ROC analizi için özel kontrol
    if (this.currentAnalysisType === 'roc') {
      // Dependent ve independent değişkenleri zorunlu
      if (field && (field.name === 'dependent_list' || field.name === 'independent_list')) {
        return this.selectedVariables[this.currentStep] && this.selectedVariables[this.currentStep].length > 0;
      }
    }

    // Linear analizi için özel kontrol
    if (this.currentAnalysisType === 'linear') {
      // Dependent ve independent değişkenleri zorunlu
      if (field && (field.name === 'dependent_list' || field.name === 'independent_list')) {
        return this.selectedVariables[this.currentStep] && this.selectedVariables[this.currentStep].length > 0;
      }
    }

    // Diğer adımlar için normal kontrol
    return this.isStepComplete(this.currentStep);
  }

  // Kredi hesaplama
  calculateRequiredCredits(): void {
    if (!this.analysis_info || !this.analysis_info.aid) {
      console.error('Cannot calculate credits: missing analysis ID');
      return;
    }

    this.calculatingCredits = true;

    // Prepare variables for credit calculation
    let variables = {};
    let additionalOptions = null;

    // Dependent analizi için özel işlem
    if (this.currentAnalysisType === 'dependent') {
      variables = {
        defineTimes: this.defineTimes || [],
        multiDefineList: this.multiDefineList || [],
        splitList: this.selectedVariables[2] || []
      };
    } else {
      // Diğer analizler için normal işlem
      Object.keys(this.filteredVariables).forEach(key => {
        variables[key] = this.filteredVariables[key];
      });
    }

    // Ki-kare analizi için rapor türü seçimini ekle
    if (this.currentAnalysisType === 'chisq') {
      additionalOptions = this.selectedReportType;
    }

    // Ortalama karşılaştırma analizi için zaman içerip içermediği bilgisini ekle
    if (this.currentAnalysisType === 'comean') {
      additionalOptions = this.isInvolveTime;
    }

    // Yeni analiz tipleri için özel işlemler
    if (['logistic_cox', 'survival', 'roc', 'linear'].includes(this.currentAnalysisType)) {
      // Şu an için ek seçenekler yok, ileride eklenebilir
    }

    // Calculate credits with simplified analysis configuration
    const options = {
      additionalOptions,
      analysisConfiguration: {
        precision: this.decimalPlaces,
        separator: this.reportFormat === 1 ? ',' : '.'
      }
    };

    this.ah.calculateCredits(this.currentAnalysisType, this.analysis_info.aid, variables, options)
      .subscribe({
        next: (response: any) => {
          this.requiredCredits = response.credits || 0;
          this.calculatingCredits = false;

          // Kullanıcının yeterli kredisi var mı bilgisini güncelle
          this.canPerformAnalysis = response.can_perform_analysis !== false;

          // Kullanıcının yeterli kredisi yoksa uyarı göster
          if (!this.canPerformAnalysis) {
            this.showInsufficientCreditsWarning();
          }
        },
        error: (error: any) => {
          console.error('Error calculating credits:', error);
          this.calculatingCredits = false;
          this.canPerformAnalysis = true; // Hata durumunda varsayılan olarak true
        }
      });
  }

  /**
   * Shows a warning modal when the user has insufficient credits
   */
  showInsufficientCreditsWarning(): void {
    this.dialog.open(ConfirmComponent, {
      data: {
        title: this.translocoService.translate('analyses.insufficient_credits.title'),
        content: this.translocoService.translate('analyses.insufficient_credits.content'),
        confirm: this.translocoService.translate('analyses.insufficient_credits.confirm'),
        cancel: this.translocoService.translate('analyses.insufficient_credits.cancel')
      }
    }).closed.subscribe((result) => {
      // Kullanıcı devam etmek istiyorsa, hiçbir şey yapma
      // İptal ederse, kredi sayfasına yönlendir
      if (!result) {
        this.dialog.open(PaymentComponent, {
        });
      }
    });
  }

  /**
   * Checks if the user has made any changes in the analysis process
   * @returns True if there are unsaved changes
   */
  hasUnsavedChanges(): boolean {
    // Check if any variables are selected in any step
    for (const stepKey in this.selectedVariables) {
      if (this.selectedVariables[stepKey] && this.selectedVariables[stepKey].length > 0) {
        return true;
      }
    }

    // For dependent analysis, check if there are any defined groups
    if (this.currentAnalysisType === 'dependent' && this.multiDefineList && this.multiDefineList.length > 0) {
      return true;
    }

    // Check if there are any reference values set
    for (const stepKey in this.filteredVariables) {
      const variables = this.filteredVariables[stepKey]?.variable || [];
      for (const variable of variables) {
        if (variable.referenceValue !== undefined && variable.referenceValue !== null) {
          return true;
        }
      }
    }

    // Check for additional options
    if (this.currentAnalysisType === 'chisq' && this.selectedReportType !== 'row') {
      return true;
    }

    if (this.currentAnalysisType === 'comean' && this.isInvolveTime) {
      return true;
    }

    // For new analysis types, check if any variables are selected
    if (['logistic_cox', 'survival', 'roc', 'linear'].includes(this.currentAnalysisType)) {
      const config = this.ah.getConfiguration(this.currentAnalysisType);
      for (const field of config.fields) {
        if (this.selectedVariables[field.order] && this.selectedVariables[field.order].length > 0) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Closes the dialog with confirmation if there are unsaved changes
   */
  closeDialog(): void {
    if (this.hasUnsavedChanges()) {
      // Open confirm dialog to ask user if they want to close without saving
      const confirmDialog = this.dialog.open(ConfirmComponent, {
        data: {
          title: this.translocoService.translate('shared.confirm.close_analysis.title'),
          content: this.translocoService.translate('shared.confirm.close_analysis.content'),
          confirm: this.translocoService.translate('shared.confirm.close_analysis.confirm'),
          cancel: this.translocoService.translate('shared.confirm.close_analysis.cancel')
        }
      });

      confirmDialog.closed.subscribe((confirmed) => {
        if (confirmed) {
          this.dialogRef.close();
        }
      });
    } else {
      // No changes, close directly
      this.dialogRef.close();
    }
  }

  /**
   * Opens the dataset view component in a dialog
   */
  viewDataset(): void {
    if (!this.analysis_info || !this.analysis_info.did) {
      this.snotifyService.error(
        this.translocoService.translate('notification.dataset.view.error.message'),
        this.translocoService.translate('notification.dataset.view.error.title'),
        {
          timeout: 3000,
          showProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          position: 'centerBottom'
        }
      );
      return;
    }

    // Get dataset information
    this.datasetService.getDatasetById(this.analysis_info.did.toString()).subscribe({
      next: (dataset) => {
        if (dataset) {
          // Open dataset view dialog
          this.dialog.open(DatasetViewComponent, {
            data: {
              title: dataset.name,
              s3_url: dataset.diagnosed_s3_url || dataset.s3_url,
              dataset_id: dataset.id
            },
            width: '95vw',
            height: '90vh'
          });
        } else {
          this.snotifyService.error(
            this.translocoService.translate('notification.dataset.view.error.message'),
            this.translocoService.translate('notification.dataset.view.error.title'),
            {
              timeout: 3000,
              showProgressBar: false,
              closeOnClick: true,
              pauseOnHover: true,
              position: 'centerBottom'
            }
          );
        }
      },
      error: (error) => {
        console.error('Error loading dataset:', error);
        this.snotifyService.error(
          this.translocoService.translate('notification.dataset.view.error.message'),
          this.translocoService.translate('notification.dataset.view.error.title'),
          {
            timeout: 3000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );
      }
    });
  }

  // Tooltip pozisyonunu hesapla
  getTooltipPosition(stepIndex: number): { top: string } {
    // Adımın sırasına göre tooltip'in top değerini hesapla
    // Her adım yaklaşık 60px yüksekliğinde, ilk adım 0 indeksli
    const baseOffset = 180; // Başlangıç offseti
    const stepHeight = 60; // Her adımın yaklaşık yüksekliği
    const topPosition = baseOffset + (stepIndex * stepHeight);

    return { top: `${topPosition}px` };
  }

  // Tooltip'i aç
  showTooltip(stepIndex: number, event: MouseEvent): void {
    event.stopPropagation(); // Olayın yayılmasını durdur
    this.activeTooltipStep = stepIndex;
  }

  // Tooltip'i kapat
  hideTooltip(event: MouseEvent): void {
    event.stopPropagation(); // Olayın yayılmasını durdur
    this.activeTooltipStep = null;
  }

  // Nominal değişkenin değer etiketlerini döndür
  getValueLabels(variable: Variable): { value: string, label: string }[] | null {
    if ((variable.measure != 'Nominal' && variable.measure != 'Ordinal') || !variable.value_labels) {
      return null;
    }

    // value_labels objesini dizi formatına dönüştür
    const labels = [];
    for (const [value, label] of Object.entries(variable.value_labels)) {
      labels.push({ value, label });
    }

    return labels.length > 0 ? labels : null;
  }

  // Değişkenin değer etiketleri var mı kontrol et
  hasValueLabels(variable: Variable): boolean {
    return (variable.measure === 'Nominal' || variable.measure === 'Ordinal') &&
      !!variable.value_labels &&
      Object.keys(variable.value_labels).length > 0;
  }

  // Değişkenin yeterli sayıda değer etiketi var mı kontrol et (en az 2)
  hasEnoughValueLabels(variable: Variable): boolean {
    return this.hasValueLabels(variable) &&
      Object.keys(variable.value_labels).length >= 2;
  }

  // Değişkenin tam olarak 2 değer etiketi var mı kontrol et
  hasExactlyTwoValueLabels(variable: Variable): boolean {
    return this.hasValueLabels(variable) &&
      Object.keys(variable.value_labels).length === 2;
  }

  // Değer etiketleri tooltip'ini göster
  showValueLabelsTooltip(variableId: number, event: MouseEvent): void {
    event.stopPropagation(); // Olayın yayılmasını durdur
    this.activeValueLabelsVariable = variableId;
  }

  // Değer etiketleri tooltip'ini gizle
  hideValueLabelsTooltip(event: MouseEvent): void {
    event.stopPropagation(); // Olayın yayılmasını durdur
    this.activeValueLabelsVariable = null;
  }

  // Gözden geçir adımında değer etiketleri tooltip'ini göster
  showReviewValueLabelsTooltip(variableId: number, event: MouseEvent): void {
    event.stopPropagation(); // Olayın yayılmasını durdur
    this.activeReviewValueLabelsVariable = variableId;
    this.activeReviewTimesForDefine = null; // Diğer tooltip'i kapat
  }

  // Gözden geçir adımında değer etiketleri tooltip'ini gizle
  hideReviewValueLabelsTooltip(event: MouseEvent): void {
    event.stopPropagation(); // Olayın yayılmasını durdur
    this.activeReviewValueLabelsVariable = null;
  }

  // Gözden geçir adımında tanım zamanları tooltip'ini göster
  showReviewTimesForDefine(variableId: number, event: MouseEvent): void {
    event.stopPropagation(); // Olayın yayılmasını durdur
    this.activeReviewTimesForDefine = variableId;
    this.activeReviewValueLabelsVariable = null; // Diğer tooltip'i kapat
  }

  // Gözden geçir adımında tanım zamanları tooltip'ini gizle
  hideReviewTimesForDefine(event: MouseEvent): void {
    event.stopPropagation(); // Olayın yayılmasını durdur
    this.activeReviewTimesForDefine = null;
  }

  // Adımlar listesinde değer etiketleri tooltip'ini göster
  showStepValueLabelsTooltip(variableId: number, event: MouseEvent): void {
    event.stopPropagation(); // Olayın yayılmasını durdur
    this.activeStepValueLabelsVariable = variableId;
    this.stepValueLabelsTooltipPosition = {
      top: event.clientY + 'px',
      left: (event.clientX + 20) + 'px'
    };
  }

  // Adımlar listesinde değer etiketleri tooltip'ini gizle
  hideStepValueLabelsTooltip(event: MouseEvent): void {
    event.stopPropagation(); // Olayın yayılmasını durdur
    this.activeStepValueLabelsVariable = null;
  }

  // Birden çok değişken seçilebilen adım mı kontrol et
  canSelectMultipleVariables(step: number): boolean {
    // Tek değişken seçilebilen adımlar için false döndür
    return !this.isStepLimitedToOneSelection(step);
  }

  isStepLimitedToOneSelection(step: number): boolean {
    const config = this.ah.getConfiguration(this.currentAnalysisType);
    const field = config.fields.find((f: any) => f.order === step);

    if (!field) return false;

    // Ayırma Değişkeni adımı
    if (field.name === 'split_list') return true;

    // Logistic Cox analizi için özel adımlar
    if (this.currentAnalysisType === 'logistic_cox' &&
      (field.name === 'status_list' || field.name === 'strata_list' || field.name === 'time_list')) {
      return true;
    }

    // Survival analizi için özel adımlar - TÜM ADIMLAR TEK SEÇİM
    if (this.currentAnalysisType === 'survival' &&
      (field.name === 'status_list' || field.name === 'factor_list' || field.name === 'time_list' || field.name === 'strata_list')) {
      return true;
    }

    // ROC analizi için özel adımlar
    if (this.currentAnalysisType === 'roc' &&
      (field.name === 'dependent_list' || field.name === 'split_list')) {
      return true;
    }

    // Linear analizi için özel adımlar - SADECE dependent_list tek seçim değil
    if (this.currentAnalysisType === 'linear' &&
      (field.name === 'split_list')) { // dependent_list kaldırıldı, çoklu seçime izin ver
      return true;
    }

    return false;
  }

  // Adımın Ayırma Değişkeni adımı olup olmadığını kontrol et (geriye dönük uyumluluk için)
  isStepSplitList(step: number): boolean {
    const config = this.ah.getConfiguration(this.currentAnalysisType);
    const field = config.fields.find((f: any) => f.order === step);

    return field && field.name === 'split_list';
  }

  // Ortalama karşılaştırma analizi için sadece 1 değişken seçildiğinde zaman seçimi gösterilmeli mi?
  shouldShowTimeOption(): boolean {
    if (this.currentAnalysisType !== 'comean') {
      return false;
    }

    // 1. adımda seçilen değişken sayısını kontrol et
    // const config = this.ah.getConfiguration(this.currentAnalysisType);
    // const factorField = config.fields.find((f: any) => f.order === 1);

    // if (!factorField) {
    //   return false;
    // }

    // Faktör adımında sadece 1 değişken seçilmiş mi?
    // this.selectedVariables[1] && this.selectedVariables[1].length === 1;
    return true;
  }

  // Tüm değişkenlerin seçili olup olmadığını kontrol et
  isAllSelected(step: number): boolean {
    const filteredVars = this.filterVariables(this.filteredVariables[step].variable);
    const selectableVars = filteredVars.filter(v => !v.disabled);

    // Seçilebilir değişken yoksa false döndür
    if (selectableVars.length === 0) {
      return false;
    }

    // Tüm seçilebilir değişkenlerin seçili olup olmadığını kontrol et
    return selectableVars.every(v => v.selected);
  }

  // Tüm değişkenleri seç/kaldır
  toggleSelectAll(step: number): void {
    const filteredVars = this.filterVariables(this.filteredVariables[step].variable);
    const selectableVars = filteredVars.filter(v => !v.disabled);

    // Tüm değişkenler seçili mi kontrol et
    const allSelected = this.isAllSelected(step);

    // Tüm değişkenlerin seçimini değiştir
    selectableVars.forEach(variable => {
      // Eğer tümü seçiliyse, seçimi kaldır; değilse, seç
      if (allSelected) {
        if (variable.selected) {
          this.toggleVariableSelection(variable, step);
        }
      } else {
        if (!variable.selected) {
          this.toggleVariableSelection(variable, step);
        }
      }
    });
  }

  // Gözden geçir adımında değişkenlerin sırasını değiştirmek için drag-drop işlevi
  dropVariable(event: CdkDragDrop<Variable[]>, stepIndex: number): void {
    // Only allow reordering if there are multiple variables
    if (this.selectedVariables[stepIndex] && this.selectedVariables[stepIndex].length > 1 &&
      event.previousIndex !== event.currentIndex) {
      // Seçilen değişkenlerin sırasını güncelle
      moveItemInArray(this.selectedVariables[stepIndex], event.previousIndex, event.currentIndex);
    }
  }

  /**
   * Removes a time entry from the list
   * @param id The ID of the time entry to remove
   */
  removeTimeFromTimeList(id: any) {
    // Ensure we maintain at least 2 time entries
    if (this.multiDefineTimes.sub.length <= 2) {
      return;
    }

    // Find the index of the time entry with the given ID
    const index = this.multiDefineTimes.sub.findIndex((time: any) => time.id === id);
    if (index === -1) return;

    // Remove the time entry
    this.multiDefineTimes.sub.splice(index, 1);

    // Update the id values for all remaining time entries
    for (let i = 0; i < this.multiDefineTimes.sub.length; i++) {
      this.multiDefineTimes.sub[i].id = i + 1;
    }
  }

  /**
   * Adds a new time entry to the list
   */
  addTimeToTimeList() {
    this.multiDefineTimes.sub.push({
      id: this.multiDefineTimes.sub.length + 1,
      variable: null,
      variableId: null,
      showVariableList: false,
      timeTr: null,
      timeEn: null,
      disabled: false,
      searchTerm: '',
      showDropdown: false,
      filteredVariables: [],
      dropdownPosition: 'bottom' // default position
    });

    // Yeni eklenen zaman için filtrelenmiş değişkenleri yükle
    const newTime = this.multiDefineTimes.sub[this.multiDefineTimes.sub.length - 1];
    this.filterVariablesForTime(newTime);
  }

  /**
   * Handles drag and drop reordering of time entries
   * @param event The CdkDragDrop event
   */
  dropTime(event: CdkDragDrop<any[]>): void {
    if (event.previousIndex !== event.currentIndex) {
      moveItemInArray(this.multiDefineTimes.sub, event.previousIndex, event.currentIndex);

      // Update the id values after reordering
      for (let i = 0; i < this.multiDefineTimes.sub.length; i++) {
        this.multiDefineTimes.sub[i].id = i + 1;
      }
    }
  }



  /**
   * Copies time entries from a previously defined time group
   * @param timeGroup The time group to copy from
   */
  copyTimes(timeGroup: any): void {
    if (!timeGroup || !timeGroup.sub || timeGroup.sub.length === 0) {
      return;
    }

    // Replace the current time entries with copies from the selected group
    this.multiDefineTimes.sub = timeGroup.sub.map((time: any, index: number) => ({
      id: index + 1,
      variable: null,
      variableId: null,
      showVariableList: false,
      timeTr: time.tr,
      timeEn: time.en,
      disabled: false
    }));

    // Close the modal
    this.showCopyTimesModal = false;
  }
}