import { Component, OnInit, Input, EventEmitter } from '@angular/core';
import { SafeHtml } from '@angular/platform-browser';

@Component({
    selector: 'app-read-more',
    templateUrl: './read-more.component.html'
})
export class ReadMoreComponent implements OnInit {

    @Input() content: string | SafeHtml;
    @Input() limit: number;
    @Input() completeWords: boolean;
    @Input() allowHtml: boolean = false;

    isContentToggled: boolean;
    nonEditedContent: string | SafeHtml;

    constructor() {

    }

    ngOnInit() {
        this.nonEditedContent = this.content;
        this.content = this.formatContent(this.content);
    }

    toggleContent() {
        this.isContentToggled = !this.isContentToggled;
        this.content = this.isContentToggled ? this.nonEditedContent : this.formatContent(this.content);
    }

    formatContent(content: string | SafeHtml) {
        const textContent = typeof content === 'string' ? content : this.getTextFromHtml(content);
        if (this.completeWords) {
            this.limit = textContent.substr(0, this.limit).lastIndexOf(' ');
        }
        const truncated = textContent.substr(0, this.limit) + '...';
        return this.allowHtml ? content : truncated;
    }

    private getTextFromHtml(html: SafeHtml): string {
        // Create a temporary div to extract text content from HTML
        const div = document.createElement('div');
        div.innerHTML = html.toString();
        return div.textContent || div.innerText || '';
    }

}
