import { Dialog } from '@angular/cdk/dialog';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ElementRef, Renderer2 } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Params, Router } from '@angular/router';
import { DiagnoseHelperService } from '@app/data/helper/diagnose.helper.service';
import { AnalysisRequirementsService } from '@app/data/services/analysis-requirements.service';
import { BreadcrumbService } from '@app/data/services/breadcrumb.service';
import { ProjectService } from '@app/data/services/project.service';
import { ProjectStateService } from '@app/modules/projects/services/project-state.service';

import { TranslocoService } from '@ngneat/transloco';
import { ReportLine } from '@app/data/models/report_line.interface';
import { Project } from '@app/data/models/project.interface';
import { AnalysisDetailsComponent } from './dialogs/analysis-details/analysis-details.component';
import { CreateAnalysisComponent } from '@app/shared/components/create-analysis/create-analysis.component';
import { AnalysisService } from '@app/data/services/analysis.service';
import { AdminHelperService } from '@app/data/helper/admin.helper.service';
declare var gsap: any;

export interface AnalysisTypes {
  type: string;
  icon: string;
  videoUrl: string;
  isNew?: boolean;
  isAvailable?: boolean;
  requirementDescription?: string;
}

@Component({
  selector: 'app-project-analysis',
  templateUrl: './analysis.component.html',
  styleUrls: ['./analysis.component.scss']
})
export class AnalysisComponent implements OnInit {
  project: Project;
  isLoading = true;
  isAnalysisView = false; // For statistical analysis view toggle
  analysisHistory: any[] = [];
  isHistoryLoading = false;

  // Power Analysis View Toggle
  powerAnalysisView: 'pre' | 'post' = 'pre'; // Default to pre-hypothesis

  // Report-list filtering properties
  searchTerm: string = '';
  isFilterDropdownOpen = false;
  filters = {
    dateRange: {
      start: null,
      end: null
    },
    sortBy: 'date_desc'
  };

  filteredHistory: any[] = [];

  // Properties for "Download All Reports" functionality
  showGetAllReports: boolean = false;
  showModal = false;
  pid: string;
  allReportsLang: string = 'tr';
  analysisTitle: string = '';
  isReportLanguageDropdownOpen: boolean = false;

  isAdmin = false;

  constructor(
    private b: BreadcrumbService,
    private dh: DiagnoseHelperService,
    private dialog: Dialog,
    private transloco: TranslocoService,
    private p: ProjectService,
    private ps: ProjectStateService,
    private route: ActivatedRoute,
    private router: Router,
    private analysisReqs: AnalysisRequirementsService,
    private a: AnalysisService,
    private ah: AdminHelperService,
    private el: ElementRef,
    private renderer: Renderer2
  ) { }

  loadProject(): void {
    this.ps.currentProject$.subscribe(project => {
      if (project) {
        this.project = project;
        this.pid = String(this.project.id);
        this.analysisTitle = this.project.name || '';

        // Only show download button for statistical analysis
        this.showGetAllReports = this.getProjectModule() === 'statistical';
      } else {
        this.route.params.subscribe((params: Params) => {
          const projectId = params['pid'];
          this.p.getProjectById(projectId).subscribe({
            next: (data) => {
              if (data) {
                this.project = data;
                this.pid = String(this.project.id);
                this.analysisTitle = this.project.name || '';
                this.showGetAllReports = this.getProjectModule() === 'statistical';
              } else {
                this.router.navigate(['/projects']);
              }
            },
            error: () => {
              this.router.navigate(['/projects']);
            }
          });
        });
      }
    });
  }

  // NEW: Get project module from localStorage or default to statistical
  getProjectModule(): 'statistical' | 'bibliometric' | 'power' {
    const storedModule = localStorage.getItem(`project_${this.project?.id}_module`);
    if (storedModule && ['statistical', 'bibliometric', 'power'].includes(storedModule)) {
      return storedModule as 'statistical' | 'bibliometric' | 'power';
    }
    return 'statistical'; // Default to statistical for existing projects
  }

  // NEW: Get analysis title based on project module
  getAnalysisTitle(): string {
    switch (this.getProjectModule()) {
      case 'statistical':
        return this.isAnalysisView ? 'header_analysis' : 'header_history';
      case 'bibliometric':
        return 'Bibliyometrik Analizler';
      case 'power':
        return 'Power Analizi';
      default:
        return 'Analizler';
    }
  }

  // NEW: Set power analysis view
  setPowerAnalysisView(view: 'pre' | 'post'): void {
    this.powerAnalysisView = view;

    // Add smooth transition animation if GSAP is available
    if (typeof gsap !== 'undefined') {
      const contentArea = this.el.nativeElement.querySelector('.power-analysis-grid');
      if (contentArea) {
        gsap.fromTo(contentArea.children,
          { opacity: 0, y: 20 },
          { opacity: 1, y: 0, duration: 0.4, stagger: 0.05, ease: "power2.out" }
        );
      }
    }
  }

  // NEW: Handle power analysis button clicks
  openPowerAnalysis(analysisType: string): void {
    console.log('Opening power analysis:', analysisType);
    // TODO: Implement power analysis dialogs/components
    // This will open specific input forms for each power analysis type

    // For now, just log the analysis type
    switch (analysisType) {
      case 'dependent_means':
        console.log('Opening dependent means analysis');
        break;
      case 'independent_means':
        console.log('Opening independent means analysis');
        break;
      case 'independent_proportions':
        console.log('Opening independent proportions analysis');
        break;
      case 'correlation':
        console.log('Opening correlation analysis');
        break;
      case 'correlation_difference':
        console.log('Opening correlation difference analysis');
        break;
      case 'odds_ratio':
        console.log('Opening odds ratio analysis');
        break;
      case 'relative_risk':
        console.log('Opening relative risk analysis');
        break;
      case 'medical_device':
        console.log('Opening medical device analysis');
        break;
      case 'mead_method':
        console.log('Opening MEAD method analysis');
        break;
      case 'power_proportions':
        console.log('Opening power for proportions analysis');
        break;
      case 'power_means':
        console.log('Opening power for means analysis');
        break;
      default:
        console.log('Unknown power analysis type:', analysisType);
    }
  }

  // NEW: Handle bibliometric analysis button clicks
  openBibliometricAnalysis(analysisType: string): void {
    console.log('Opening bibliometric analysis:', analysisType);
    // TODO: Implement bibliometric analysis dialogs/components

    switch (analysisType) {
      case 'publication_trends':
        console.log('Opening publication trends analysis');
        break;
      case 'author_analysis':
        console.log('Opening author analysis');
        break;
      case 'journal_analysis':
        console.log('Opening journal analysis');
        break;
      case 'keyword_network':
        console.log('Opening keyword network analysis');
        break;
      case 'citation_analysis':
        console.log('Opening citation analysis');
        break;
      case 'geographic_distribution':
        console.log('Opening geographic distribution analysis');
        break;
      case 'research_areas':
        console.log('Opening research areas analysis');
        break;
      default:
        console.log('Unknown bibliometric analysis type:', analysisType);
    }
  }

  navigateToDetail(report: ReportLine) {
    // report.id yerine report.credit_usage?.creditable_id kullan
    const reportContentId = report.credit_usage?.creditable_id || report.id;

    this.router.navigate(['/reports', reportContentId], {
      state: {
        report: report,
        projectName: report.credit_usage?.project_name,
        projectId: report.credit_usage?.project_id
      }
    });
  }

  urlSubscription: any;
  ngOnInit(): void {
    if (this.ah.isRole('admin')) {
      this.isAdmin = true;
      if (this.isAdmin) {
        this.analyses.unshift({
          type: 'logistic_cox',
          icon: 'logistic_cox',
          videoUrl: 'https://www.youtube.com/embed/example?si=example',
          isNew: true
        },
          {
            type: 'survival',
            icon: 'survival',
            videoUrl: 'https://www.youtube.com/embed/example?si=example',
            isNew: true
          },
          {
            type: 'roc',
            icon: 'roc',
            videoUrl: 'https://www.youtube.com/embed/example?si=example',
            isNew: true
          },
          {
            type: 'linear',
            icon: 'linear',
            videoUrl: 'https://www.youtube.com/embed/example?si=example',
            isNew: true
          }, {
          type: 'comean',
          icon: 'comean',
          videoUrl: 'https://www.youtube.com/embed/1Z9v6Jjv9Z0?si=6TlZDztMXrhh18nz',
          isNew: true
        });
      }
    }
    this.loadProject();

    // Only run diagnose analysis for statistical projects
    if (this.getProjectModule() === 'statistical') {
      this.diagnoseAnalysis();
    }

    this.allReportsLang = localStorage.getItem('activeLang') || 'tr';

    // Update the route subscription
    this.urlSubscription =
      this.route.url.subscribe(() => {
        this.isAnalysisView = !this.router.url.includes('/history');
      }).unsubscribe();

    // Initialize animations after view init
    setTimeout(() => {
      this.initializeCardAnimations();
    }, 100);
  }

  initializeCardAnimations(): void {
    // Check if GSAP is available
    if (typeof gsap !== 'undefined') {
      const cards = this.el.nativeElement.querySelectorAll('.analysis-card');

      // Animate cards on load
      gsap.fromTo(cards,
        {
          opacity: 0,
          y: 30,
          scale: 0.9
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.6,
          delay: (index: number) => index * 0.1,
          ease: "back.out(1.7)",
          stagger: 0.1
        }
      );
    }
  }

  diagnoseAnalysis(): void {
    // Only run for statistical analysis and when project has datasets
    if (this.getProjectModule() !== 'statistical' || !this.project?.datasets?.length) {
      this.isLoading = false;
      return;
    }

    this.isLoading = true;
    this.dh.diagnoseAnalysis(String(this.project.datasets[0].id)).subscribe({
      next: (availabilityMap) => {
        this.analyses = this.analyses.map(analysis => ({
          ...analysis,
          isAvailable: availabilityMap[analysis.type],
          requirementDescription: this.analysisReqs.getRequirementDescription(analysis.type)
        }));
        this.isLoading = false;

        // Re-initialize animations after data is loaded
        setTimeout(() => {
          this.initializeCardAnimations();
        }, 50);
      },
      error: (error) => {
        console.error('Analysis diagnosis failed:', error);
        this.isLoading = false;
      }
    });
  }

  analyses: AnalysisTypes[] = [
    {
      type: 'descriptive',
      icon: 'descriptive',
      videoUrl: 'https://www.youtube.com/embed/SrpBGvnDmqo?si=9Ms-b4tHWF49kGbz'
    },
    {
      type: 'single',
      icon: 'single',
      videoUrl: 'https://www.youtube.com/embed/19GiPTLOcUg?si=KzIRls2HKjVF7F_R'
    },
    {
      type: 'multi',
      icon: 'multi',
      videoUrl: 'https://www.youtube.com/embed/aANHllnl6vQ?si=GPyzxJ830cp1ZwZU'
    },
    {
      type: 'dependent',
      icon: 'dependent',
      videoUrl: 'https://www.youtube.com/embed/Zi-oUSgBxcI?si=6TlZDztMXrhh18nz'
    },
    {
      type: 'correlation',
      icon: 'correlation',
      videoUrl: 'https://www.youtube.com/embed/gn3lfdLzx-o?si=wszUguCBzdjRAs8n'
    },
    {
      type: 'chisq',
      icon: 'chisq',
      videoUrl: 'https://www.youtube.com/embed/NgdXEQnzxx4?si=r6tltKFWuR1ysrCV'
    }
  ];

  /**
   * Handle card click with animations and navigation
   */
  handleCardClick(analysis: AnalysisTypes, event: Event): void {
    if (!analysis.isAvailable) {
      return;
    }

    const card = event.currentTarget as HTMLElement;
    const clickIndicator = card.querySelector('.click-indicator') as HTMLElement;

    // Create ripple effect with GSAP if available
    if (typeof gsap !== 'undefined' && clickIndicator) {
      gsap.set(clickIndicator, {
        scale: 0,
        opacity: 1
      });

      gsap.to(clickIndicator, {
        scale: 3,
        opacity: 0,
        duration: 0.6,
        ease: "power2.out"
      });

      // Card click animation
      gsap.to(card, {
        scale: 0.98,
        duration: 0.1,
        yoyo: true,
        repeat: 1,
        ease: "power2.inOut"
      });
    }

    // Navigate to analysis details after a short delay for animation
    setTimeout(() => {
      this.showDetails(analysis);
    }, 150);
  }

  /**
   * Get analysis description based on type
   */
  getAnalysisDescription(type: string): string {
    const descriptions = {
      'descriptive': 'Comprehensive descriptive statistics including measures of central tendency, dispersion, and distribution characteristics.',
      'single': 'In-depth analysis of individual variables including normality tests, outlier detection, and distribution analysis.',
      'multi': 'Complex analysis involving multiple variables, including multivariate statistics and relationship exploration.',
      'dependent': 'Analysis focusing on dependent variables and their relationships with independent factors.',
      'correlation': 'Statistical analysis to determine relationships and correlations between different variables.',
      'chisq': 'Chi-square tests for independence and goodness of fit for categorical data analysis.',
      'logistic_cox': 'Advanced regression analysis for binary outcomes and survival data modeling.',
      'survival': 'Time-to-event analysis including Kaplan-Meier curves and hazard ratio calculations.',
      'roc': 'Receiver Operating Characteristic analysis for evaluating binary classification models.',
      'linear': 'Linear regression analysis for continuous dependent variables and predictive modeling.',
      'comean': 'Advanced comparative mean analysis with statistical significance testing.'
    };

    return descriptions[type] || 'Advanced statistical analysis for your research needs.';
  }

  startAnalysis(type: any): void {
    const analysis = this.analyses.find(a => a.type === type);
    if (!analysis?.isAvailable) {
      // Show error message or tooltip
      return;
    }

    const dialogRef = this.dialog.open(CreateAnalysisComponent, {
      data: {
        selectedAnalyseType: type,
        aid: this.project.datasets[0].analyses[0].id,
        did: this.project.datasets[0].id,
        pid: this.project.id,
        cid: this.project.datasets[0].analyses[0].analysis_configuration.id
      }
    });

    dialogRef.closed.subscribe(result => {
      if (result) {
        this.loadProject();
      }
    });
  }

  toggleView(): void {
    this.isAnalysisView = !this.isAnalysisView;
    if (!this.isAnalysisView) {
      this.router.navigate(['history'], { relativeTo: this.route });
    } else {
      this.router.navigate(['/projects', this.project.id, 'analysis']);
    }
  }

  filterHistory(): void {
    let filtered = [...this.analysisHistory];

    // Text search
    if (this.searchTerm.trim()) {
      const searchTermLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(analysis =>
        analysis.title.toLowerCase().includes(searchTermLower)
      );
    }

    // Date range filter
    if (this.filters.dateRange.start || this.filters.dateRange.end) {
      filtered = filtered.filter(analysis => {
        const analysisDate = new Date(analysis.created_at);
        const start = this.filters.dateRange.start ? new Date(this.filters.dateRange.start) : null;
        const end = this.filters.dateRange.end ? new Date(this.filters.dateRange.end) : null;

        if (start && end) {
          return analysisDate >= start && analysisDate <= end;
        } else if (start) {
          return analysisDate >= start;
        } else if (end) {
          return analysisDate <= end;
        }
        return true;
      });
    }

    // Sorting
    filtered = this.sortHistory(filtered);

    this.filteredHistory = filtered;
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR');
  }

  private sortHistory(history: any[]): any[] {
    return history.sort((a, b) => {
      switch (this.filters.sortBy) {
        case 'date_desc':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'date_asc':
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        case 'name_asc':
          return a.title.localeCompare(b.title);
        case 'name_desc':
          return b.title.localeCompare(a.title);
        default:
          return 0;
      }
    });
  }

  toggleFilterDropdown(): void {
    this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
  }

  resetFilters(): void {
    this.filters = {
      dateRange: {
        start: null,
        end: null
      },
      sortBy: 'date_desc'
    };
    this.searchTerm = '';
    this.filterHistory();
  }

  getActiveFilterCount(): number {
    let count = 0;
    if (this.filters.dateRange.start || this.filters.dateRange.end) count++;
    if (this.filters.sortBy !== 'date_desc') count++;
    return count;
  }

  viewAnalysis(analysisId: string): void {
    this.router.navigate(['/projects', this.project.id, 'analyses', analysisId]);
  }

  showDetails(analysis: any): void {
    const dialogRef = this.dialog.open(AnalysisDetailsComponent, {
      data: { analysis },
      maxWidth: '100vw',
      width: '600px'
    });

    dialogRef.closed.subscribe(result => {
      if (result) {
        this.startAnalysis(result);
      }
    });
  }

  slugify(text: string): string {
    const turkishMap = {
      'ş': 's',
      'Ş': 'S',
      'ç': 'c',
      'Ç': 'C',
      'ğ': 'g',
      'Ğ': 'G',
      'ü': 'u',
      'Ü': 'U',
      'ö': 'o',
      'Ö': 'O',
      'ı': 'i',
      'İ': 'I'
    };

    return text
      .toLowerCase()
      .replace(/[şŞçÇğĞüÜöÖıİ]/g, (match) => turkishMap[match])
      .replace(/[^a-z0-9 -]/g, '') // Remove all non-alphanumeric characters except spaces and dashes
      .replace(/\s+/g, '-') // Replace spaces with dashes
      .replace(/-+/g, '-'); // Replace multiple dashes with a single dash
  }

  toggleGetAllReports() {
    this.showGetAllReports = !this.showGetAllReports;
  }

  toggleReportLanguageDropdown() {
    this.isReportLanguageDropdownOpen = !this.isReportLanguageDropdownOpen;
  }

  downloadAllReportDocx(lang: string = 'tr') {
    this.showModal = true;
    this.isReportLanguageDropdownOpen = false; // Close dropdown

    this.a.getAllReportContentDocxById(this.pid, lang).subscribe({
      next: (data) => {
        const a = document.createElement('a');
        const objectUrl = URL.createObjectURL(data);
        a.href = objectUrl;
        a.download = lang === 'tr' ?
          this.slugify(this.analysisTitle) + '_favori_raporlar.docx' :
          this.slugify(this.analysisTitle) + '_favorite_reports.docx';
        a.click();
        setTimeout(() => {
          this.showModal = false;
        }, 1000);
        URL.revokeObjectURL(objectUrl);
      },
      error: (error) => {
        console.error('Error downloading all reports:', error);
        this.showModal = false;
      }
    });
  }

  /**
 * Get tooltip message for disabled analysis
 */
  getAnalysisTooltip(analysisType: string): string {
    if (!this.project?.datasets?.length) {
      return this.transloco.translate('project_detail.analysis.tooltip.no_dataset');
    }

    // Return predefined tooltip for each analysis type
    return this.transloco.translate(`project_detail.analysis.tooltip.${analysisType}`);
  }

  /**
   * Get tooltip class based on analysis availability
   */
  getTooltipClass(analysis: AnalysisTypes): string {
    if (analysis.isAvailable) return '';

    return 'tooltip-disabled';
  }


}