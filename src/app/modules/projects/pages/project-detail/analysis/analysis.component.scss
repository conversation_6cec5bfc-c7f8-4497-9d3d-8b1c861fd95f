// Analysis component specific styles
.analysis-card {
  position: relative;
  background: white;
  border-radius: 1.5rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  // Available state
  &.border-brand-green-300 {
    border-color: #9ae6b4;
    background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 8%, white 20%);

    &:hover {
      border-color: #68d391;
      background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 12%, white 25%);
    }
  }

  // Disabled state
  &.border-gray-300 {
    border-color: #d2d6dc;
    background: #f9fafb;
    opacity: 0.7;
    cursor: not-allowed;

    &:hover {
      transform: none;
      box-shadow:
        0 10px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
  }

  // Icon container animations
  .icon-container {
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.5s;
    }
  }

  &:hover .icon-container::before {
    left: 100%;
  }

  // Click ripple effect
  .click-indicator {
    position: absolute;
    border-radius: 50%;
    pointer-events: none;
    z-index: 1000;
  }

  // Card title hover effect
  h3 {
    transition: color 0.3s ease;
  }

  // New badge pulse animation
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }

  // Status indicator
  .w-3.h-3.rounded-full {
    transition: all 0.3s ease;
    box-shadow: 0 0 0 0 rgba(72, 187, 120, 0.7);

    &.bg-brand-green-500 {
      animation: statusPulse 2s infinite;
    }
  }

  @keyframes statusPulse {
    0% {
      box-shadow: 0 0 0 0 rgba(72, 187, 120, 0.7);
    }
    70% {
      box-shadow: 0 0 0 4px rgba(72, 187, 120, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(72, 187, 120, 0);
    }
  }

  // Line clamp utility
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  // Hover effects for text elements
  &:hover {
    .text-neutral-600 {
      color: #4a5568;
    }
  }

  // Disabled card hover effects override
  &.opacity-60:hover {
    .icon-container {
      transform: none !important;
    }

    h3 {
      color: inherit !important;
    }
  }
}

// Power Analysis Toggle Buttons
.power-toggle-container {
  .toggle-button {
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.3s ease;
    }

    &:hover::before {
      left: 100%;
    }

    &.active {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
}

// Enhanced section headers for power analysis
.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    border-radius: 1px;
  }

  .icon-wrapper {
    padding: 0.75rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      transition:
        width 0.3s ease,
        height 0.3s ease;
    }

    &:hover::before {
      width: 100%;
      height: 100%;
    }

    &.blue {
      background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    }

    &.green {
      background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    }

    &.purple {
      background: linear-gradient(135deg, #e9d5ff 0%, #d8b4fe 100%);
    }
  }

  h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #111827;
    margin: 0;
    position: relative;
  }

  p {
    margin: 0;
    font-style: italic;
  }
}

// Power Analysis and Bibliometric Analysis Button Styles
.power-analysis-button,
.bibliometric-analysis-button {
  position: relative;
  background: white;
  border-radius: 1.5rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  width: 100%;
  border: 2px solid #9ae6b4;
  background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 8%, white 20%);
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border-color: #68d391;
    background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 12%, white 25%);
  }

  // Hover overlay
  &::before {
    content: "";
    position: absolute;
    inset: 0;
    transition: opacity 0.3s;
    opacity: 0;
    background: linear-gradient(
      135deg,
      rgba(16, 185, 129, 0.05) 0%,
      rgba(16, 185, 129, 0.1) 100%
    );
    pointer-events: none;
  }

  &:hover::before {
    opacity: 1;
  }

  // Icon container animations
  .icon-container {
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    overflow: hidden;
    width: 4rem;
    height: 4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 1rem;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.39);

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.5s;
    }

    ng-icon {
      color: white;
      font-size: 2rem;
      filter: brightness(0) invert(1);
    }
  }

  &:hover .icon-container {
    transform: scale(1.1) rotate(3deg);

    &::before {
      left: 100%;
    }
  }

  // Title hover effect
  h4 {
    transition: color 0.3s ease;
    color: #1f2937;
    font-weight: 700;
    font-size: 1.25rem;
    margin: 0;
  }

  &:hover h4 {
    color: #10b981;
  }

  // Description text
  p {
    transition: color 0.3s ease;
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
  }

  &:hover p {
    color: #4b5563;
  }

  // Header structure similar to analysis cards
  .flex.items-center.gap-3 {
    margin-bottom: 0.75rem;
  }
}

// Power Analysis specific colors
.power-analysis-button {
  // Pre-hypothesis buttons
  &.dependent-means .icon-container {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.39);
  }

  &.independent-means .icon-container {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.39);
  }

  &.independent-proportions .icon-container {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    box-shadow: 0 4px 14px 0 rgba(139, 92, 246, 0.39);
  }

  &.correlation .icon-container {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 14px 0 rgba(245, 158, 11, 0.39);
  }

  &.correlation-difference .icon-container {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    box-shadow: 0 4px 14px 0 rgba(239, 68, 68, 0.39);
  }

  &.odds-ratio .icon-container {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.39);
  }

  &.relative-risk .icon-container {
    background: linear-gradient(135deg, #eab308 0%, #ca8a04 100%);
    box-shadow: 0 4px 14px 0 rgba(234, 179, 8, 0.39);
  }

  &.medical-device .icon-container {
    background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
    box-shadow: 0 4px 14px 0 rgba(236, 72, 153, 0.39);
  }

  &.mead-method .icon-container {
    background: linear-gradient(135deg, #14b8a6 0%, #0d9488 100%);
    box-shadow: 0 4px 14px 0 rgba(20, 184, 166, 0.39);
  }

  // Post-hypothesis buttons
  &.power-proportions .icon-container {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.39);
  }

  &.power-means .icon-container {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.39);
  }
}

// Bibliometric Analysis specific colors
.bibliometric-analysis-button {
  &.publication-trends .icon-container {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    box-shadow: 0 4px 14px 0 rgba(139, 92, 246, 0.39);
  }

  &.author-analysis .icon-container {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.39);
  }

  &.journal-analysis .icon-container {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.39);
  }

  &.keyword-network .icon-container {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 14px 0 rgba(245, 158, 11, 0.39);
  }

  &.citation-analysis .icon-container {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    box-shadow: 0 4px 14px 0 rgba(239, 68, 68, 0.39);
  }

  &.geographic-distribution .icon-container {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.39);
  }

  &.research-areas .icon-container {
    background: linear-gradient(135deg, #14b8a6 0%, #0d9488 100%);
    box-shadow: 0 4px 14px 0 rgba(20, 184, 166, 0.39);
  }
}

// Click ripple effect for power and bibliometric analysis buttons
.power-analysis-button,
.bibliometric-analysis-button {
  // Click ripple effect container
  .click-indicator {
    position: absolute;
    width: 2.5rem;
    height: 2.5rem;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    opacity: 0;
    pointer-events: none;
    top: 50%;
    left: 50%;
    background: rgba(16, 185, 129, 0.3);
    z-index: 10;
  }
}

// Section headers styling
.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;

  .icon-wrapper {
    padding: 0.75rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;

    &.blue {
      background-color: #dbeafe;
    }

    &.green {
      background-color: #d1fae5;
    }

    &.purple {
      background-color: #e9d5ff;
    }
  }

  h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #111827;
    margin: 0;
  }
}

// Grid responsive adjustments
.analysis-grid {
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }

  @media (min-width: 1025px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

// Power and Bibliometric grid responsive adjustments
.power-analysis-grid,
.bibliometric-analysis-grid {
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  @media (min-width: 1025px) and (max-width: 1280px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  @media (min-width: 1281px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

// Custom scrollbar for the main container
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f7fafc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;

    &:hover {
      background: #a0aec0;
    }
  }
}

// Enhanced focus states for accessibility
.analysis-card,
.power-analysis-button,
.bibliometric-analysis-button {
  &:focus {
    outline: 2px solid #4299e1;
    outline-offset: 2px;
  }

  &:focus-visible {
    outline: 2px solid #4299e1;
    outline-offset: 2px;
  }
}

// Loading states
.analysis-card.loading {
  .icon-container {
    background: #e2e8f0;
    animation: shimmer 1.5s ease-in-out infinite;
  }

  h3,
  p {
    background: #e2e8f0;
    color: transparent;
    border-radius: 0.25rem;
    animation: shimmer 1.5s ease-in-out infinite;
  }
}

@keyframes shimmer {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

// Animation for power and bibliometric buttons
.power-analysis-button,
.bibliometric-analysis-button {
  animation: slideInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);

  @for $i from 1 through 12 {
    &:nth-child(#{$i}) {
      animation-delay: #{($i - 1) * 0.1}s;
    }
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive improvements for smaller screens
@media (max-width: 640px) {
  .power-analysis-button,
  .bibliometric-analysis-button {
    padding: 1rem;

    h4 {
      font-size: 0.875rem;
    }

    p {
      font-size: 0.75rem;
    }

    .icon-container {
      width: 3rem;
      height: 3rem;

      ng-icon {
        font-size: 1.5rem;
      }
    }
  }

  .section-header {
    .icon-wrapper {
      padding: 0.5rem;

      ng-icon {
        font-size: 1.25rem;
      }
    }

    h3 {
      font-size: 1.25rem;
    }
  }
}

// Dark mode support (if needed in future)
@media (prefers-color-scheme: dark) {
  .power-analysis-button,
  .bibliometric-analysis-button {
    background: rgba(31, 41, 55, 0.8);
    border-color: rgba(75, 85, 99, 0.3);
    color: #f9fafb;

    &:hover {
      background: rgba(31, 41, 55, 0.9);
    }

    h4 {
      color: #f9fafb;
    }

    p {
      color: #d1d5db;
    }
  }

  .section-header h3 {
    color: #f9fafb;
  }

  // analysis.component.scss - Tooltip stilleri ekle

  // Tooltip custom styling
  ::ng-deep .analysis-tooltip {
    background-color: #1f2937 !important;
    color: white !important;
    font-size: 12px !important;
    line-height: 1.4 !important;
    border-radius: 8px !important;
    padding: 8px 12px !important;
    max-width: 280px !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;

    .mat-mdc-tooltip {
      font-size: 12px !important;
    }
  }

  // Analysis card disabled state improvements
  .analysis-card {
    &:not(.opacity-60) {
      .icon-container {
        background: linear-gradient(135deg, #10b981, #059669);
      }
    }

    &.opacity-60 {
      .icon-container {
        background: linear-gradient(135deg, #9ca3af, #6b7280);
      }

      &:hover {
        transform: none !important;
        border-color: #d1d5db !important;
        box-shadow: none !important;
      }
    }
  }

  // Disabled indicator animation
  .disabled-indicator {
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }
}
