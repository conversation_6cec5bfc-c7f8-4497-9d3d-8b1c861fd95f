<div class="h-full">
    <div class="flex flex-col h-full p-8 overflow-y-auto bg-white border-neutral-150 rounded-3xl"
        *transloco="let t;read: 'project_detail.analysis'">
        <div class="flex items-center justify-between">
            <div class="flex justify-between w-full">
                <div class="flex flex-col">
                    <h6 class="text-3xl font-bold text-neutral-950">
                        {{ getAnalysisTitle() }}
                    </h6>
                    <p *ngIf="powerAnalysisView === 'pre'" class="ml-auto text-sm text-gray-600">Çalışma öncesi güç
                        analizi
                        ve örneklem büyüklüğü hesaplama</p>
                    <p *ngIf="powerAnalysisView === 'post'" class="ml-auto text-sm text-gray-600">Mevcut verilerle güç
                        hesaplama ve etki büyüklüğü analizi</p>
                </div>
                <!-- Power Analysis Toggle Buttons -->
                <div *ngIf="getProjectModule() === 'power'" class="flex justify-center power-toggle-container">
                    <div class="inline-flex p-1 bg-gray-100 rounded-full shadow-inner">
                        <button (click)="setPowerAnalysisView('pre')"
                            [class]="powerAnalysisView === 'pre' ? 'bg-white text-blue-600 shadow-sm active' : 'text-gray-600 hover:text-blue-600'"
                            class="flex items-center gap-2 px-6 py-3 text-sm font-medium transition-all duration-200 rounded-full toggle-button">
                            <ng-icon name="lucideFlaskConical" class="text-lg"></ng-icon>
                            <span>Hipotez Öncesi</span>
                        </button>
                        <button (click)="setPowerAnalysisView('post')"
                            [class]="powerAnalysisView === 'post' ? 'bg-white text-green-600 shadow-sm active' : 'text-gray-600 hover:text-green-600'"
                            class="flex items-center gap-2 px-6 py-3 text-sm font-medium transition-all duration-200 rounded-full toggle-button">
                            <ng-icon name="lucideCalculator" class="text-lg"></ng-icon>
                            <span>Hipotez Sonrası</span>
                        </button>
                    </div>
                </div>
            </div>
            <div class="flex items-center gap-2">
                <!-- Download button for statistical analysis -->
                <div *ngIf="getProjectModule() === 'statistical' && !isAnalysisView && showGetAllReports"
                    class="relative">
                    <button (click)="toggleReportLanguageDropdown()" class="secondary-blue-button text-nowrap">
                        <ng-icon name="lucideDownload" class="text-lg"></ng-icon>
                        {{ t('download_favorites') }}
                    </button>

                    <!-- Language Selection Dropdown -->
                    <div *ngIf="isReportLanguageDropdownOpen"
                        class="absolute right-0 z-50 mt-2 bg-white border shadow-lg rounded-xl">
                        <div class="p-2">
                            <button (click)="downloadAllReportDocx('tr')"
                                class="flex items-center w-full gap-2 px-4 py-2 text-sm text-left transition-colors rounded-lg hover:bg-gray-100">
                                🇹🇷 Türkçe
                            </button>
                            <button (click)="downloadAllReportDocx('en')"
                                class="flex items-center w-full gap-2 px-4 py-2 text-sm text-left transition-colors rounded-lg hover:bg-gray-100">
                                🇬🇧 English
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Toggle view button for statistical analysis -->
                <button *ngIf="getProjectModule() === 'statistical'" (click)="toggleView()"
                    class="primary-blue-button text-nowrap">
                    <ng-icon [name]="isAnalysisView ? 'lucideHistory' : 'lucideLayoutGrid'" class="text-lg"></ng-icon>
                    {{ isAnalysisView ? t('show_history') : t('show_analyses') }}
                </button>
            </div>
        </div>

        <div class="flex-1 pt-2 mt-6 overflow-y-auto">
            <!-- Statistical Analysis Grid View -->
            <div *ngIf="getProjectModule() === 'statistical' && isAnalysisView"
                class="grid gap-6 xl:grid-cols-4 lg:grid-cols-3 md:grid-cols-2">
                <div *ngFor="let analysis of analyses; let i = index"
                    class="relative overflow-hidden transition-all duration-300 ease-out border-2 cursor-pointer analysis-card group rounded-3xl"
                    [ngClass]="{
                        'border-brand-green-300 bg-gradient-to-br from-brand-green-50 via-white to-brand-green-50 hover:border-brand-green-400 hover:shadow-xl hover:-translate-y-2': analysis.isAvailable,
                        'border-gray-300 bg-gray-50 opacity-60 cursor-not-allowed': !analysis.isAvailable
                    }" [attr.data-index]="i" (click)="handleCardClick(analysis, $event)"
                    [matTooltip]="!analysis.isAvailable ? getAnalysisTooltip(analysis.type) : ''"
                    [matTooltipPosition]="'above'" [matTooltipShowDelay]="50" [matTooltipHideDelay]="50">

                    <!-- Hover overlay -->
                    <div class="absolute inset-0 transition-opacity duration-300 opacity-0 bg-gradient-to-br from-brand-green-100/20 to-brand-green-200/20 group-hover:opacity-100"
                        [class.group-hover:opacity-0]="!analysis.isAvailable"></div>

                    <!-- Click ripple effect container -->
                    <div
                        class="absolute w-10 h-10 transform -translate-x-1/2 -translate-y-1/2 rounded-full opacity-0 pointer-events-none click-indicator top-1/2 left-1/2 bg-brand-green-500/30">
                    </div>

                    <!-- New Badge -->
                    <div class="absolute top-0 right-0 flex items-center gap-1 px-3 py-1.5 text-xs font-bold text-white shadow-lg bg-gradient-to-r from-purple-500 to-indigo-500 rounded-bl-xl animate-pulse z-10"
                        *ngIf="analysis.isNew">
                        {{ t('new') }}
                        <ng-icon name="lucideSparkles" class="w-3 h-3"></ng-icon>
                    </div>

                    <div class="relative z-10 flex flex-col h-full ">
                        <!-- Header -->
                        <div class="flex items-center gap-4">
                            <div class="flex items-center justify-center w-16 h-16 transition-transform duration-300 shadow-lg icon-container rounded-2xl bg-gradient-to-br from-brand-green-500 to-brand-green-600 group-hover:scale-110 group-hover:rotate-3"
                                [class.group-hover:scale-100]="!analysis.isAvailable"
                                [class.group-hover:rotate-0]="!analysis.isAvailable">
                                <img [src]="'assets/icons/' + analysis.icon + '.svg'"
                                    class="w-8 h-8 filter brightness-0 invert">
                            </div>
                            <div class="flex-1">
                                <h3 class="text-xl font-bold transition-colors duration-300 text-neutral-800 group-hover:text-brand-green-600"
                                    [class.group-hover:text-neutral-800]="!analysis.isAvailable">
                                    {{ t('analyses.' + analysis.type) }}
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistical Analysis History View -->
            <app-analysis-history *ngIf="getProjectModule() === 'statistical' && !isAnalysisView"
                [analysisId]="project.datasets[0].analyses[0].id">
            </app-analysis-history>

            <!-- Power Analysis Content -->
            <div *ngIf="getProjectModule() === 'power'" class="space-y-8">
                <!-- Pre-hypothesis Section -->
                <div *ngIf="powerAnalysisView === 'pre'" class="space-y-6">
                    <div class="grid gap-4 power-analysis-grid md:grid-cols-2 lg:grid-cols-3">
                        <!-- Dependent Means Difference -->
                        <button (click)="openPowerAnalysis('dependent_means')"
                            class="power-analysis-button dependent-means">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-blue-100 rounded-lg icon-container">
                                    <ng-icon name="lucideBarChart3" class="text-lg text-blue-600"></ng-icon>
                                </div>
                                <h4 class="font-semibold text-gray-900">Bağımlı Ortalamalar Arası Fark</h4>
                            </div>
                            <p class="text-sm text-gray-600">Paired samples t-test için güç analizi</p>
                        </button>

                        <!-- Independent Means Difference -->
                        <button (click)="openPowerAnalysis('independent_means')"
                            class="power-analysis-button independent-means">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-green-100 rounded-lg icon-container">
                                    <ng-icon name="lucideBarChart4" class="text-lg text-green-600"></ng-icon>
                                </div>
                                <h4 class="font-semibold text-gray-900">Bağımsız Ortalamalar Arası Fark</h4>
                            </div>
                            <p class="text-sm text-gray-600">Independent samples t-test için güç analizi</p>
                        </button>

                        <!-- Independent Proportions Difference -->
                        <button (click)="openPowerAnalysis('independent_proportions')"
                            class="power-analysis-button independent-proportions">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-purple-100 rounded-lg icon-container">
                                    <ng-icon name="lucidePieChart" class="text-lg text-purple-600"></ng-icon>
                                </div>
                                <h4 class="font-semibold text-gray-900">Bağımsız Oranlar Arası Fark</h4>
                            </div>
                            <p class="text-sm text-gray-600">Two proportions test için güç analizi</p>
                        </button>

                        <!-- Correlation Coefficient -->
                        <button (click)="openPowerAnalysis('correlation')" class="power-analysis-button correlation">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-orange-100 rounded-lg icon-container">
                                    <ng-icon name="lucideActivity" class="text-lg text-orange-600"></ng-icon>
                                </div>
                                <h4 class="font-semibold text-gray-900">Korelasyon Katsayısı</h4>
                            </div>
                            <p class="text-sm text-gray-600">Pearson korelasyon için güç analizi</p>
                        </button>

                        <!-- Correlation Coefficients Difference -->
                        <button (click)="openPowerAnalysis('correlation_difference')"
                            class="power-analysis-button correlation-difference">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-red-100 rounded-lg icon-container">
                                    <ng-icon name="lucideTrendingUp" class="text-lg text-red-600"></ng-icon>
                                </div>
                                <h4 class="font-semibold text-gray-900">Korelasyon Katsayıları Arası Fark</h4>
                            </div>
                            <p class="text-sm text-gray-600">İki korelasyon katsayısı karşılaştırması</p>
                        </button>

                        <!-- Odds Ratio Significance -->
                        <button (click)="openPowerAnalysis('odds_ratio')" class="power-analysis-button odds-ratio">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-indigo-100 rounded-lg icon-container">
                                    <ng-icon name="lucideScale" class="text-lg text-indigo-600"></ng-icon>
                                </div>
                                <h4 class="font-semibold text-gray-900">Odds Oranı Önemliliği</h4>
                            </div>
                            <p class="text-sm text-gray-600">Odds ratio significance test</p>
                        </button>

                        <!-- Relative Risk (RR) Significance -->
                        <button (click)="openPowerAnalysis('relative_risk')"
                            class="power-analysis-button relative-risk">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-yellow-100 rounded-lg icon-container">
                                    <ng-icon name="lucideTarget" class="text-lg text-yellow-600"></ng-icon>
                                </div>
                                <h4 class="font-semibold text-gray-900">Göreli Risk (RR) Önemliliği</h4>
                            </div>
                            <p class="text-sm text-gray-600">Relative risk significance test</p>
                        </button>

                        <!-- Medical Device Performance -->
                        <button (click)="openPowerAnalysis('medical_device')"
                            class="power-analysis-button medical-device">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-pink-100 rounded-lg icon-container">
                                    <ng-icon name="lucideStethoscope" class="text-lg text-pink-600"></ng-icon>
                                </div>
                                <h4 class="font-semibold text-gray-900">Tıbbi Cihaz Performansı</h4>
                            </div>
                            <p class="text-sm text-gray-600">Tanı testi/prevalans analizi</p>
                        </button>

                        <!-- MEAD Method for Animal Studies -->
                        <button (click)="openPowerAnalysis('mead_method')" class="power-analysis-button mead-method">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-teal-100 rounded-lg icon-container">
                                    <ng-icon name="lucideBeaker" class="text-lg text-teal-600"></ng-icon>
                                </div>
                                <h4 class="font-semibold text-gray-900">Hayvan Deneyleri (MEAD)</h4>
                            </div>
                            <p class="text-sm text-gray-600">MEAD yöntemi ile örneklem büyüklüğü</p>
                        </button>
                    </div>
                </div>

                <!-- Post-hypothesis Section -->
                <div *ngIf="powerAnalysisView === 'post'" class="space-y-6">
                    <div class="grid gap-4 md:grid-cols-2">
                        <!-- Power for Proportions Difference -->
                        <button (click)="openPowerAnalysis('power_proportions')"
                            class="power-analysis-button post-hypothesis power-proportions">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-green-100 rounded-lg icon-container">
                                    <ng-icon name="lucideZap" class="text-lg text-green-600"></ng-icon>
                                </div>
                                <h4 class="font-semibold text-gray-900">Oranlar Arası Fark İçin Güç</h4>
                            </div>
                            <p class="text-sm text-gray-600">Mevcut verilerle güç hesaplama</p>
                        </button>

                        <!-- Power for Means Difference -->
                        <button (click)="openPowerAnalysis('power_means')"
                            class="power-analysis-button post-hypothesis power-means">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-green-100 rounded-lg icon-container">
                                    <ng-icon name="lucideBolt" class="text-lg text-green-600"></ng-icon>
                                </div>
                                <h4 class="font-semibold text-gray-900">Ortalamalar Arası Fark İçin Güç</h4>
                            </div>
                            <p class="text-sm text-gray-600">Mevcut verilerle güç hesaplama</p>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Bibliometric Analysis Content -->
            <div *ngIf="getProjectModule() === 'bibliometric'" class="space-y-6">
                <div class="grid gap-6 bibliometric-analysis-grid md:grid-cols-2 lg:grid-cols-3">
                    <!-- Publication Trends -->
                    <button (click)="openBibliometricAnalysis('publication_trends')"
                        class="bibliometric-analysis-button publication-trends">
                        <div class="flex items-center gap-3 mb-3">
                            <div class="p-2 bg-purple-100 rounded-lg icon-container">
                                <ng-icon name="lucideTrendingUp" class="text-lg text-purple-600"></ng-icon>
                            </div>
                            <h4 class="font-semibold text-gray-900">Publication Trends</h4>
                        </div>
                        <p class="text-sm text-gray-600">Yayın trendleri ve zaman serisi analizi</p>
                    </button>

                    <!-- Author Analysis -->
                    <button (click)="openBibliometricAnalysis('author_analysis')"
                        class="bibliometric-analysis-button author-analysis">
                        <div class="flex items-center gap-3 mb-3">
                            <div class="p-2 bg-blue-100 rounded-lg icon-container">
                                <ng-icon name="lucideUsers" class="text-lg text-blue-600"></ng-icon>
                            </div>
                            <h4 class="font-semibold text-gray-900">Author Analysis</h4>
                        </div>
                        <p class="text-sm text-gray-600">Yazar üretkenliği ve işbirliği analizi</p>
                    </button>

                    <!-- Journal Analysis -->
                    <button (click)="openBibliometricAnalysis('journal_analysis')"
                        class="bibliometric-analysis-button journal-analysis">
                        <div class="flex items-center gap-3 mb-3">
                            <div class="p-2 bg-green-100 rounded-lg icon-container">
                                <ng-icon name="lucideNewspaper" class="text-lg text-green-600"></ng-icon>
                            </div>
                            <h4 class="font-semibold text-gray-900">Journal Analysis</h4>
                        </div>
                        <p class="text-sm text-gray-600">Dergi etki faktörü ve yayın dağılımı</p>
                    </button>

                    <!-- Keyword Network -->
                    <button (click)="openBibliometricAnalysis('keyword_network')"
                        class="bibliometric-analysis-button keyword-network">
                        <div class="flex items-center gap-3 mb-3">
                            <div class="p-2 bg-orange-100 rounded-lg icon-container">
                                <ng-icon name="lucideNetwork" class="text-lg text-orange-600"></ng-icon>
                            </div>
                            <h4 class="font-semibold text-gray-900">Keyword Network</h4>
                        </div>
                        <p class="text-sm text-gray-600">Anahtar kelime ağı ve tema analizi</p>
                    </button>

                    <!-- Citation Analysis -->
                    <button (click)="openBibliometricAnalysis('citation_analysis')"
                        class="bibliometric-analysis-button citation-analysis">
                        <div class="flex items-center gap-3 mb-3">
                            <div class="p-2 bg-red-100 rounded-lg icon-container">
                                <ng-icon name="lucideQuote" class="text-lg text-red-600"></ng-icon>
                            </div>
                            <h4 class="font-semibold text-gray-900">Citation Analysis</h4>
                        </div>
                        <p class="text-sm text-gray-600">Atıf analizi ve etki ölçümü</p>
                    </button>

                    <!-- Geographic Distribution -->
                    <button (click)="openBibliometricAnalysis('geographic_distribution')"
                        class="bibliometric-analysis-button geographic-distribution">
                        <div class="flex items-center gap-3 mb-3">
                            <div class="p-2 bg-indigo-100 rounded-lg icon-container">
                                <ng-icon name="lucideGlobe" class="text-lg text-indigo-600"></ng-icon>
                            </div>
                            <h4 class="font-semibold text-gray-900">Geographic Distribution</h4>
                        </div>
                        <p class="text-sm text-gray-600">Coğrafi dağılım ve uluslararası işbirliği</p>
                    </button>

                    <!-- Research Areas -->
                    <button (click)="openBibliometricAnalysis('research_areas')"
                        class="bibliometric-analysis-button research-areas">
                        <div class="flex items-center gap-3 mb-3">
                            <div class="p-2 bg-teal-100 rounded-lg icon-container">
                                <ng-icon name="lucideAtom" class="text-lg text-teal-600"></ng-icon>
                            </div>
                            <h4 class="font-semibold text-gray-900">Research Areas</h4>
                        </div>
                        <p class="text-sm text-gray-600">Araştırma alanları ve disiplinler arası analiz</p>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Download Progress Modal -->
    <div *ngIf="showModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div class="p-6 bg-white rounded-lg shadow-xl">
            <div class="flex flex-col items-center">
                <div
                    class="w-16 h-16 mb-4 border-4 border-t-4 rounded-full border-brand-green-500 border-t-transparent animate-spin">
                </div>
                <p class="text-lg font-medium text-gray-800">{{ t('downloading_reports') }}</p>
            </div>
        </div>
    </div>
</div>