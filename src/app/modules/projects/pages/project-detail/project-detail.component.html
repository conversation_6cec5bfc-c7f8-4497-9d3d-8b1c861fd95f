<!-- project-detail.component.html - Updated version -->
<div *transloco="let t;read 'project_detail.header'" class="flex flex-col h-full gap-6">
    <!-- Skeleton Loading -->
    <div *ngIf="initializing" class="flex flex-col flex-none h-full gap-6">
        <div class="flex-none p-4 pb-0 bg-white border border-neutral-150 rounded-3xl animate-pulse">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2 p-4">
                    <div class="w-48 h-8 bg-gray-200 rounded"></div>
                </div>
            </div>
            <div class="pl-4 mb-6">
                <div class="w-32 h-4 bg-gray-200 rounded"></div>
            </div>
            <div class="flex w-full gap-4 px-2">
                <div class="w-20 h-8 bg-gray-200 rounded"></div>
                <div class="w-20 h-8 bg-gray-200 rounded"></div>
                <div class="w-20 h-8 bg-gray-200 rounded"></div>
                <div class="w-20 h-8 bg-gray-200 rounded"></div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div *ngIf="!initializing" class="flex flex-col h-full gap-4">
        <!-- Header -->
        <div class="flex p-4 pb-0 bg-white border border-neutral-150 rounded-3xl">
            <!-- Header -->
            <div class="w-full">
                <div class="flex flex-col pl-2 ">
                    <div class="flex items-center justify-between gap-2">
                        <div class="flex items-center gap-2">
                            <h1 class="text-3xl font-bold truncate text-neutral-950 max-w-160"
                                [matTooltip]="project.name">
                                {{ project.name }}
                            </h1>

                            <!-- NEW: Project Module Flair -->
                            <div *ngIf="getProjectModuleTitle()"
                                class="inline-flex items-center px-3 py-1 text-sm font-medium rounded-full"
                                [ngClass]="getProjectModuleClass()">
                                {{getProjectModuleTitle()}}
                            </div>

                            <!-- Existing Demo Project Type Flair -->
                            <div *ngIf="project?.project_type && (project.project_type === 'demo_template' || project.project_type === 'demo')"
                                class="inline-flex items-center px-3 py-1 text-sm font-medium rounded-full" [ngClass]="{'bg-purple-100 text-purple-800': project.project_type === 'demo_template', 
                              'bg-blue-100 text-blue-800': project.project_type === 'demo'}">
                                {{project.project_type === 'demo_template' ? 'Demo Şablonu' : 'Demo Proje'}}
                            </div>

                            <button
                                class="flex items-center justify-center flex-shrink-0 transition-colors rounded-full hover:bg-gray-100 size-10"
                                (click)="toggleFavorite($event)"
                                [title]="project?.favorite ? t('remove_favorite') : t('add_favorite')">
                                <ng-icon [name]="project?.favorite ? 'tablerStarFilled' : 'tablerStarOff'"
                                    class="text-2xl" [class.text-yellow-500]="project?.favorite"
                                    [class.text-gray-400]="!project?.favorite"
                                    [ngClass]="{'fill-current': project?.favorite}"></ng-icon>
                            </button>
                        </div>

                        <div class="flex items-center gap-2">
                            <button (click)="openEditProjectDialog()" class="secondary-blue-button">
                                <ng-icon name="lucidePencilLine" class="text-xl"></ng-icon>
                                {{t('edit')}}
                            </button>
                            <!-- Admin Toolbar -->
                            <div *ngIf="isAdmin && canUseAdminFunctions()" class="flex">
                                <div class="relative">
                                    <!-- Dropdown Toggle Button -->
                                    <button (click)="toggleAdminDropdown()" [matTooltip]="t('admin_tools.menu')"
                                        class="secondary-blue-button">
                                        <ng-icon name="lucideSettings" class="text-2xl"></ng-icon>
                                    </button>

                                    <!-- Dropdown Menu -->
                                    <div *ngIf="adminDropdownOpen"
                                        class="absolute right-0 z-50 mt-2 overflow-hidden bg-white border border-gray-200 shadow-xl w-72 rounded-2xl animate-fade-in"
                                        (click)="$event.stopPropagation()">
                                        <!-- Clone Project -->
                                        <button (click)="cloneProject()" [disabled]="isCloning"
                                            class="flex items-center w-full gap-3 px-4 py-3 text-sm text-left text-gray-700 transition-all border-b border-gray-100 menu-item hover:text-green-700 hover:bg-green-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                            <div
                                                class="flex items-center justify-center w-10 h-10 bg-green-100 rounded-xl">
                                                <ng-icon *ngIf="!isCloning" name="lucideBookCopy"
                                                    class="text-lg text-green-600"></ng-icon>
                                                <ng-icon *ngIf="isCloning" name="hugeLoading03"
                                                    class="text-lg text-green-600 animate-spin"></ng-icon>
                                            </div>
                                            <div class="flex-1">
                                                <div class="font-semibold">{{t('admin_tools.clone')}}</div>
                                                <div class="text-xs text-gray-500">Mevcut projenin kopyasını oluştur
                                                </div>
                                            </div>
                                            <div *ngIf="isCloning" class="flex items-center">
                                                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                            </div>
                                        </button>

                                        <!-- Recalculate Project - Only for statistical and bibliometric -->
                                        <button *ngIf="getProjectModule() !== 'power'" (click)="recalculateProject()"
                                            [disabled]="isRecalculating"
                                            class="flex items-center w-full gap-3 px-4 py-3 text-sm text-left text-gray-700 transition-all border-b border-gray-100 menu-item hover:text-blue-700 hover:bg-blue-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                            <div
                                                class="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-xl">
                                                <ng-icon *ngIf="!isRecalculating" name="lucideRefreshCcw"
                                                    class="text-lg text-blue-600"></ng-icon>
                                                <ng-icon *ngIf="isRecalculating" name="hugeLoading03"
                                                    class="text-lg text-blue-600 animate-spin"></ng-icon>
                                            </div>
                                            <div class="flex-1">
                                                <div class="font-semibold">{{t('admin_tools.recalculate')}}</div>
                                                <div class="text-xs text-gray-500">Proje verilerini yeniden hesapla
                                                </div>
                                            </div>
                                            <div *ngIf="isRecalculating" class="flex items-center">
                                                <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                            </div>
                                        </button>

                                        <!-- Compare Reports - Only for statistical and bibliometric -->
                                        <button *ngIf="getProjectModule() !== 'power'" (click)="compareReports()"
                                            [disabled]="isComparing"
                                            class="flex items-center w-full gap-3 px-4 py-3 text-sm text-left text-gray-700 transition-all border-b border-gray-100 menu-item hover:text-purple-700 hover:bg-purple-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                            <div
                                                class="flex items-center justify-center w-10 h-10 bg-purple-100 rounded-xl">
                                                <ng-icon *ngIf="!isComparing" name="lucideGitCompareArrows"
                                                    class="text-lg text-purple-600"></ng-icon>
                                                <ng-icon *ngIf="isComparing" name="hugeLoading03"
                                                    class="text-lg text-purple-600 animate-spin"></ng-icon>
                                            </div>
                                            <div class="flex-1">
                                                <div class="font-semibold">{{t('admin_tools.compare')}}</div>
                                                <div class="text-xs text-gray-500">Farklı raporları analiz et</div>
                                            </div>
                                            <div *ngIf="isComparing" class="flex items-center">
                                                <div class="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                                            </div>
                                        </button>

                                        <!-- Change Project Type -->
                                        <button (click)="showProjectTypeOptions()" [disabled]="isChangingType"
                                            class="flex items-center w-full gap-3 px-4 py-3 text-sm text-left text-gray-700 transition-all menu-item hover:text-amber-700 hover:bg-amber-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                            <div
                                                class="flex items-center justify-center w-10 h-10 bg-amber-100 rounded-xl">
                                                <ng-icon *ngIf="!isChangingType" name="lucideFolderPen"
                                                    class="text-lg text-amber-600"></ng-icon>
                                                <ng-icon *ngIf="isChangingType" name="hugeLoading03"
                                                    class="text-lg text-amber-600 animate-spin"></ng-icon>
                                            </div>
                                            <div class="flex-1">
                                                <div class="font-semibold">{{t('admin_tools.change_project_settings')}}
                                                </div>
                                                <div class="text-xs text-gray-500">Proje tipi ve modül ayarları</div>
                                            </div>
                                            <div class="flex items-center">
                                                <ng-icon name="lucideChevronRight"
                                                    class="text-sm text-gray-400"></ng-icon>
                                            </div>
                                        </button>

                                        <!-- Project Settings Sub-menu -->
                                        <div *ngIf="showProjectTypes" class="border-t border-gray-100 bg-gray-50">
                                            <div class="px-4 py-2">
                                                <!-- Project Types Section -->
                                                <div class="mb-4">
                                                    <div
                                                        class="mb-2 text-xs font-medium tracking-wide text-gray-500 uppercase">
                                                        Proje Tipi</div>
                                                    <div class="space-y-1">
                                                        <button
                                                            *ngFor="let option of ['normal', 'demo_template', 'demo']"
                                                            (click)="selectProjectType(option)"
                                                            [class.bg-blue-100]="project?.project_type === option"
                                                            class="w-full px-3 py-2 text-sm text-left text-gray-700 transition-all rounded-lg hover:bg-white hover:text-amber-700">
                                                            {{ getLabel(option) }}
                                                        </button>
                                                    </div>
                                                </div>

                                                <!-- Analysis Modules Section -->
                                                <div>
                                                    <div
                                                        class="mb-2 text-xs font-medium tracking-wide text-gray-500 uppercase">
                                                        Analiz Modülü</div>
                                                    <div class="space-y-1">
                                                        <button
                                                            *ngFor="let module of ['statistical', 'bibliometric', 'power']"
                                                            (click)="selectProjectType(module)"
                                                            [class.bg-green-100]="getProjectModule() === module"
                                                            class="w-full px-3 py-2 text-sm text-left text-gray-700 transition-all rounded-lg hover:bg-white hover:text-amber-700">
                                                            {{ getLabel(module) }}
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>
                    <div class="flex items-center justify-start gap-2 text-xs text-neutral-600">
                        <span>#{{project?.id}}</span>
                        <span *ngIf="project?.used_credits" class="flex">
                            <img src="assets/icons/istacoin.svg" alt="" class="mr-1 size-4">
                            {{project.used_credits}} {{t('credits_used')}}
                        </span>
                        <p class="flex items-center text-sm text-neutral-600 ">
                            <ng-icon name="lucideClock4" class="mr-1"></ng-icon>
                            {{t('last_updated')}} {{ project?.updated_at | date: 'dd/MM/yyyy HH:mm' }}
                        </p>
                    </div>
                </div>
                <!-- 1. Project Description Card -->
                <div class="flex flex-col justify-end py-2 pl-2 overflow-hidden">
                    <div class="h-full overflow-y-auto">
                        <p *ngIf="project?.description" class="text-sm font-normal break-words text-blue-950/80">
                            {{project.description}}
                        </p>
                    </div>
                </div>

                <!-- UPDATED: Navigation Tabs with Module-based Visibility -->
                <div class="flex w-full">
                    <!-- Overview Tab - Always visible -->
                    <a [routerLink]="['./overview']" (click)="setActiveTab('overview')"
                        [ngClass]="{'border-status-info-500 text-status-info-500': activeTab === 'overview'}"
                        class="flex items-center justify-center px-3 py-2 border-b-2">
                        <span class="text-xl">{{t('overview')}}</span>
                    </a>

                    <!-- Dataset Tab - Hidden for power analysis -->
                    <a *ngIf="shouldShowTab('dataset')" [routerLink]="['./dataset']"
                        (click)="!isTabDisabled('dataset') ? setActiveTab('dataset') : $event.preventDefault()"
                        [ngClass]="{
                         'border-status-info-500 text-status-info-500': activeTab === 'dataset',
                         'opacity-50 cursor-not-allowed pointer-events-none': isTabDisabled('dataset')
                       }" class="flex items-center justify-center px-3 py-2 border-b-2">
                        <span class="text-xl">{{t('dataset')}}</span>
                    </a>

                    <!-- Analysis Tab - Always visible but content differs -->
                    <a [routerLink]="['./analysis']"
                        (click)="!isTabDisabled('analysis') ? setActiveTab('analysis') : $event.preventDefault()"
                        [ngClass]="{
                         'border-status-info-500 text-status-info-500': activeTab === 'analysis',
                         'opacity-50 cursor-not-allowed pointer-events-none': isTabDisabled('analysis')
                       }" class="flex items-center justify-center px-3 py-2 border-b-2">
                        <span class="text-xl">{{t('analysis')}}</span>
                    </a>

                    <!-- Settings Tab - Always visible -->
                    <a [routerLink]="['./settings']" (click)="setActiveTab('settings')"
                        [ngClass]="{'border-status-info-500 text-status-info-500': activeTab === 'settings'}"
                        class="flex items-center justify-center px-3 py-2 border-b-2">
                        <span class="text-xl">{{t('settings')}}</span>
                    </a>

                    <!-- Members Tab - Always visible -->
                    <a [routerLink]="['./members']" (click)="setActiveTab('members')"
                        [ngClass]="{'border-status-info-500 text-status-info-500': activeTab === 'members'}"
                        class="flex items-center justify-center px-3 py-2 border-b-2">
                        <span class="text-xl">{{t('members')}}</span>
                    </a>
                </div>
            </div>
        </div>

        <div class="flex-1 min-h-0">
            <router-outlet></router-outlet>
        </div>
    </div>
</div>