import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { Component, OnInit } from '@angular/core';
import { ProjectService } from '@app/data/services/project.service';
import { AuthService } from '@app/modules/auth/auth.service';
import { Dialog } from '@angular/cdk/dialog';
import { TranslocoService } from '@ngneat/transloco';
import { CreateProjectComponent } from '../../dialogs/create-project/create-project.component';
import { BreadcrumbService } from '@app/data/services/breadcrumb.service';
import { Project } from '@app/data/models/project.interface';
import { DateAdapter, MAT_DATE_LOCALE } from '@angular/material/core';
import { Router } from '@angular/router';
import { Breadcrumb } from '@app/data/models/breadcrumb.interface';
import { SnotifyService } from 'ng-alt-snotify';
import { AdminHelperService } from '@app/data/helper/admin.helper.service';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';

@Component({
  selector: 'app-projects-list',
  templateUrl: './projects-list.component.html',
  styleUrls: ['./projects-list.component.scss'],
  animations: [
    trigger('fadeIn', [
      state('void', style({ opacity: '0' })),
      state('*', style({ opacity: '*' })),
      transition('void <=> *', [animate('0.2s ease-in')]),
    ]),
  ],
  providers: [
    { provide: MAT_DATE_LOCALE, useValue: 'tr-TR' }
  ]
})
export class ProjectsListComponent implements OnInit {

  hideProjectListSkeleton: boolean = false;
  hideProjectDetailsSkeleton: boolean = false;

  disabledButtonId: number;
  isLoading = true;
  skeletonItems = Array(5).fill(0); // Show 5 skeleton items while loading

  constructor(
    private p: ProjectService,
    private auth: AuthService,
    private transloco: TranslocoService,
    public dialog: Dialog,
    private breadcrumbService: BreadcrumbService,
    private router: Router,
    private dateAdapter: DateAdapter<any>,
    private snotifyService: SnotifyService,
    private ah: AdminHelperService,
  ) {
    // Set initial locale
    this.setDateLocale(this.transloco.getActiveLang());

    // Listen for language changes
    this.transloco.langChanges$.subscribe(lang => {
      this.setDateLocale(lang);
      this.updatePageTitle();
    });
    this.setDateInputLocale();
    this.dateAdapter.setLocale(this.transloco.getActiveLang());

    this.transloco.langChanges$.subscribe(lang => {
      this.dateAdapter.setLocale(lang);
    });

    this.isAdmin = this.ah.isRole('admin');
  }

  currentLang = 'tr-TR';
  projects: Project[] = [];
  filteredProjects: Project[] = [];
  searchTerm: string = '';
  isAdmin: boolean = false;
  isCloning: boolean = false;
  isRecalculating: boolean = false;
  isComparing: boolean = false;

  isFilterDropdownOpen = false;
  filters = {
    status: 'all', // 'all', 'new', 'ready', 'needs_review', 'needs_dataset'
    dateRange: {
      start: null,
      end: null
    },
    hasDataset: 'all', // 'all', 'with', 'without'
    sortBy: 'position' // 'position', 'date_desc', 'date_asc', 'name_asc', 'name_desc'
  };

  showOnlyFavorites: boolean = false;

  ngOnInit(): void {
    this.updateBreadcrumbs();
    this.loadProjects();

    // Sayfa başlığını ayarla
    this.updatePageTitle();
  }

  /**
   * Sayfa başlığını günceller
   */
  private updatePageTitle(): void {
    // "route adı | istabot" formatını uygula
    this.transloco.selectTranslate('project_list.my_projects').subscribe(title => {
      document.title = `${title} | istabot`;
    });
  }

  updateBreadcrumbs(): void {
    const breadcrumbs: Breadcrumb[] = [
      {
        label: 'navigation.projects',
        link: '/projects',
        icon: 'lucideLayers'  // Sidebar ile uyumlu ikon
      },
    ];
    this.breadcrumbService.setBreadcrumbs(breadcrumbs);
  }

  loadProjects(): void {
    this.isLoading = true;
    this.p.getProjectAll().subscribe(
      (data) => {
        this.projects = data;
        // Debug: Check if projects have positions

        this.filterProjects();
        this.isLoading = false;
      },
      (error) => {
        console.error('Projeler yüklenirken hata oluştu:', error);
        this.auth.logout();
        this.router.navigate(['/login']);
        this.isLoading = false;
      }
    );
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR');
  }

  getProjectStatus(project: Project): { text: string; class: string } {
    // Check if project is created within last 24 hours
    const oneDay = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    const projectDate = new Date(project.created_at).getTime();
    const now = new Date().getTime();

    if ((now - projectDate) < oneDay) {
      return {
        text: 'status.new_project',
        class: 'bg-brand-blue-100 text-brand-blue-500'
      };
    } else if (project.datasets?.some(d => d.diagnosed_s3_url)) {
      return {
        text: 'status.ready_for_analysis',
        class: 'bg-brand-green-100 text-brand-green-500'
      };
    } else if (project.datasets?.length > 0) {
      return {
        text: 'status.needs_review',
        class: 'bg-status-warning-100 text-status-warning-500'
      };
    } else if (project.datasets?.length == 0) {
      return {
        text: 'status.needs_dataset',
        class: 'bg-status-error-100 text-status-error-500'
      };
    }
    return {
      text: 'status.new_project',
      class: 'bg-brand-blue-100 text-brand-blue-500'
    };
  }

showCreateProject(): void {
  const dialogRef = this.dialog.open(CreateProjectComponent, {
    data: {},
    width: 'calc(50%)',
    // height: 'calc(100% - 4rem)',
  });

    dialogRef.closed.subscribe((result) => {
      if (result) {
        this.loadProjects();
      }
    });
  }

  toggleFilterDropdown(): void {
    this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
  }

  resetFilters(): void {
    this.filters = {
      status: 'all',
      dateRange: {
        start: null,
        end: null
      },
      hasDataset: 'all',
      sortBy: 'position'
    };
    this.searchTerm = '';
    this.showOnlyFavorites = false;
    this.filterProjects();
  }

  getActiveFilterCount(): number {
    let count = 0;
    if (this.filters.status !== 'all') count++;
    if (this.filters.hasDataset !== 'all') count++;
    if (this.filters.dateRange.start || this.filters.dateRange.end) count++;
    if (this.filters.sortBy !== 'position') count++;
    if (this.showOnlyFavorites) count++;
    return count;
  }

  filterProjects(): void {
    let filtered = [...this.projects];

    // Favorite filter
    if (this.showOnlyFavorites) {
      filtered = filtered.filter(project => project.favorite);
    }

    // Text search filter
    if (this.searchTerm.trim()) {
      const searchTermNormalized = this.searchTerm.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '').trim();
      filtered = filtered.filter(project => {
        const nameMatch = project.name.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '').includes(searchTermNormalized);
        const datasetMatch = project.datasets?.some(dataset =>
          dataset.name.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '').includes(searchTermNormalized)
        );
        return nameMatch || datasetMatch;
      });
    }

    // Status filter
    if (this.filters.status !== 'all') {
      filtered = filtered.filter(project => {
        const status = this.getProjectStatus(project).text.toLowerCase();
        switch (this.filters.status) {
          case 'new': return status === 'status.new_project';
          case 'ready': return status === 'status.ready_for_analysis';
          case 'needs_review': return status === 'status.needs_review';
          case 'needs_dataset': return status === 'status.needs_dataset';
          default: return true;
        }
      });
    }

    // Dataset filter
    if (this.filters.hasDataset !== 'all') {
      filtered = filtered.filter(project => {
        const hasDataset = project.datasets?.length > 0;
        return this.filters.hasDataset === 'with' ? hasDataset : !hasDataset;
      });
    }

    // Date range filter with proper date handling
    if (this.filters.dateRange.start || this.filters.dateRange.end) {
      filtered = filtered.filter(project => {
        const projectDate = new Date(project.created_at);
        const start = this.filters.dateRange.start ? new Date(this.filters.dateRange.start) : null;
        const end = this.filters.dateRange.end ? new Date(this.filters.dateRange.end) : null;

        if (start && end) {
          // Set end date to end of day
          end.setHours(23, 59, 59, 999);
          return projectDate >= start && projectDate <= end;
        } else if (start) {
          return projectDate >= start;
        } else if (end) {
          end.setHours(23, 59, 59, 999);
          return projectDate <= end;
        }
        return true;
      });
    }

    // Sort results
    filtered = this.sortProjects(filtered);

    this.filteredProjects = filtered;

  }

  private sortProjects(projects: Project[]): Project[] {
    return projects.sort((a, b) => {
      switch (this.filters.sortBy) {
        case 'position':
          if (a.position !== undefined && b.position !== undefined) {
            return a.position - b.position;
          }
          return 0;
          // If no position, fall back to created date
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'date_desc':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'date_asc':
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        case 'name_asc':
          return a.name.localeCompare(b.name);
        case 'name_desc':
          return b.name.localeCompare(a.name);
        default:
          return 0;
      }
    });
  }

  // Add this helper method to close filter dropdown when clicking outside
  onClickOutside(event: Event) {
    if (this.isFilterDropdownOpen) {
      this.isFilterDropdownOpen = false;
    }
  }

  private setDateLocale(lang: string) {
    // Map language codes to locales
    const localeMap = {
      'tr': 'tr-TR',
      'en': 'en-US'
    };

    const locale = localeMap[lang] || 'tr-TR';
    this.dateAdapter.setLocale(locale);
    this.currentLang = locale;
  }

  // Helper method to format date string for input
  formatDateForInput(date: Date | null): string {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  }

  getDateInputLocale(): string {
    return this.currentLang;
  }

  formatDateForDisplay(date: string | null): string {
    if (!date) return '';
    return new Date(date).toLocaleDateString(this.getDateInputLocale());
  }

  private setDateInputLocale(): void {
    // Get browser locale or use TR as default
    const browserLocale = navigator.language;
    this.currentLang = browserLocale.startsWith('tr') ? 'tr-TR' : 'en-GB';

    // Listen for language changes
    this.transloco.langChanges$.subscribe(lang => {
      this.currentLang = lang === 'tr' ? 'tr-TR' : 'en-GB';
    });
  }

  // Route to project detail page
  navigateToDetail(project: Project) {
    this.router.navigate(['/projects', project.id], {
      state: {
        project
      }
    });
  }

  // Favorite functionality using the service
  toggleFavorite(event: Event, projectId: number) {
    // Stop the click event from propagating to the parent button
    event.stopPropagation();

    // Find the project in the list
    const project = this.projects.find(p => p.id === projectId);
    if (!project) return;

    // Toggle the favorite status
    const previousStatus = project.favorite;
    project.favorite = !project.favorite;

    // Update the filtered projects as well
    const filteredProject = this.filteredProjects.find(p => p.id === projectId);
    if (filteredProject) {
      filteredProject.favorite = project.favorite;
    }

    // Call the API to update the favorite status
    this.p.toggleProjectFavorite(projectId.toString()).subscribe({
      next: () => {
        // Success notification
        this.snotifyService.success(
          this.transloco.translate(project.favorite ? 'notification.project.favorite.added' : 'notification.project.favorite.removed'),
          this.transloco.translate('notification.project.favorite.title'),
          {
            timeout: 2000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );

        // Refresh filters to update UI if necessary
        this.filterProjects();
      },
      error: (error) => {
        console.error(error);
        // Revert the UI change if the API call fails
        project.favorite = previousStatus;
        if (filteredProject) {
          filteredProject.favorite = previousStatus;
        }

        this.snotifyService.error(
          this.transloco.translate('notification.project.favorite.error'),
          this.transloco.translate('notification.project.favorite.title'),
          {
            timeout: 3000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );
      }
    });
  }

  toggleFavoritesFilter() {
    this.showOnlyFavorites = !this.showOnlyFavorites;
    this.filterProjects();
  }

  // Drag and drop functionality for sorting using the service
  onProjectListDrop(event: CdkDragDrop<Project[]>) {
    // Don't allow reordering if we're in the middle of another operation or if favorites filter is active
    if (this.isCloning || this.isRecalculating || this.isComparing || this.showOnlyFavorites || this.filters.sortBy !== 'position') {
      return;
    }

    // Get the previous order
    const previousList = [...this.filteredProjects];

    // Update the array order
    moveItemInArray(this.filteredProjects, event.previousIndex, event.currentIndex);

    // Get the moved project and its new position
    const movedProject = this.filteredProjects[event.currentIndex];
    const newPosition = event.currentIndex + 1;

    // Update all positions in the filtered list
    this.filteredProjects.forEach((project, index) => {
      project.position = index + 1;
    });

    // Call API to update only the moved project's position
    this.p.updateProjectPosition(movedProject.id.toString(), newPosition).subscribe({
      next: () => {
        // Success notification
        this.snotifyService.success(
          this.transloco.translate('notification.project.position.success'),
          this.transloco.translate('notification.project.position.title'),
          {
            timeout: 2000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );

        // Update the position in the main projects array
        this.filteredProjects.forEach((project) => {
          const mainProjectIndex = this.projects.findIndex(p => p.id === project.id);
          if (mainProjectIndex > -1) {
            this.projects[mainProjectIndex].position = project.position;
          }
        });

        // Re-sort the main projects array by position
        this.projects.sort((a, b) => (a.position || 0) - (b.position || 0));

        // No need to re-filter here as positions are already updated
      },
      error: (error) => {
        console.error('Position update error:', error);
        // Revert the UI change if the API call fails
        this.filteredProjects = previousList;

        this.snotifyService.error(
          this.transloco.translate('notification.project.position.error'),
          this.transloco.translate('notification.project.position.title'),
          {
            timeout: 3000,
            showProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            position: 'centerBottom'
          }
        );
      }
    });
  }

  getTotalCreditsUsed(project: Project): number {
    let totalCredits = 0;

    // Projenin raporları varsa
    if (project.reports && project.reports.length > 0) {
      project.reports.forEach(report => {
        // Eğer raporda credit_usage alanı varsa
        if (report.credit_usage && report.credit_usage.used_credit) {
          totalCredits += report.credit_usage.used_credit;
        }

        // Alternatif olarak credit_usages dizisi varsa
        if (report.credit_usages && report.credit_usages.length > 0) {
          report.credit_usages.forEach(usage => {
            if (usage.used_credit) {
              totalCredits += usage.used_credit;
            }
          });
        }
      });
    }

    // Eğer projede doğrudan used_credits alanı varsa (fallback)
    if (project.used_credits) {
      totalCredits += project.used_credits;
    }

    return totalCredits;
  }

  clearSearch() {
    this.searchTerm = '';
    this.filterProjects();
  }
}