// create-project.component.ts - Updated version with module concept

import { Component, ElementRef, HostListener, Inject, NgZ<PERSON>, OnInit, ViewChild } from '@angular/core';
import { HttpClient, HttpEventType } from '@angular/common/http';
import { Dialog, DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { ProjectService } from '@app/data/services/project.service';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { DatasetService } from '@app/data/services/dataset.service';
import { SnotifyService } from 'ng-alt-snotify';
import { Observable } from 'rxjs';
import { TranslocoService } from '@ngneat/transloco';
import { DiagnoseHelperService } from '@app/data/helper/diagnose.helper.service';
import { ExcelService } from '@app/data/services/excel.service';
import { ConfirmComponent } from '@app/shared/components/confirm/confirm.component';
import { DiagnoseComponent } from '@app/modules/diagnose/dialogs/diagnose/diagnose.component';
import { VideoEmbedComponent } from '@app/shared/components/video-embed/video-embed.component';

@Component({
  selector: 'app-create-project',
  templateUrl: './create-project.component.html',
  styleUrls: ['./create-project.component.scss'],
  animations: [
    trigger('slideInOut', [
      state('in', style({ transform: 'translateX(0)' })),
      state('out', style({ transform: 'translateX(100%)' })),
      transition('void => in', [
        style({ transform: 'translateX(100%)' }),
        animate('300ms ease-out')
      ]),
      transition('in => out', [
        animate('300ms ease-in', style({ transform: 'translateX(100%)' }))
      ])
    ])
  ]
})
export class CreateProjectComponent implements OnInit {
  hasReports: boolean = false;
  project: any = null;
  selectedFile: File | null = null;
  uploadProgress: number | null = null;
  isDiagnoseOpen = false;
  isDragging = false;
  isLoading = false;

  // Step system properties
  currentStep: number = 1;
  selectedProjectModule: 'statistical' | 'bibliometric' | 'power' | null = null; // Changed from selectedProjectType

  // Form and file properties
  form: FormGroup;
  submitted: boolean = false;
  projectName: string = '';
  protectId: number;
  datasetId: number;
  file: File;
  isFileUploaded = null;
  s3_url = null;
  example_url = '';
  fileName = '';
  actualFileName = '';
  isSuccessFromistabot = null;
  animationState = 'in';

  // Example URLs for different project modules
  exampleUrls = {
    statistical: {
      tr: 'https://drive.google.com/uc?export=download&id=1AfACIYat1CoZ-IkNUxutYW6W0ToJ6MIZ',
      en: 'https://drive.google.com/uc?export=download&id=1vRrE2kbX24iSaKGZARpFBQv1U4BcEzA6'
    },
    bibliometric: {
      tr: 'https://drive.google.com/uc?export=download&id=BIBLIOMETRIC_TR_FILE_ID', // Gerçek dosya ID'leriyle değiştirin
      en: 'https://drive.google.com/uc?export=download&id=BIBLIOMETRIC_EN_FILE_ID'
    },
    power: {
      tr: 'https://drive.google.com/uc?export=download&id=POWER_TR_FILE_ID',
      en: 'https://drive.google.com/uc?export=download&id=POWER_EN_FILE_ID'
    }
  };

  @ViewChild('fileInput') fileInput: ElementRef;

  constructor(
    public dialog: Dialog,
    @Inject(DIALOG_DATA) public data: any,
    public dialogRef: DialogRef<any>,
    private formBuilder: FormBuilder,
    private d: DatasetService,
    private projectService: ProjectService,
    private snotifyService: SnotifyService,
    private ngZone: NgZone,
    private transloco: TranslocoService,
    private diagnoseHelper: DiagnoseHelperService,
    private excelService: ExcelService,
  ) {
    this.form = this.formBuilder.group({
      name: [''],
    });
  }

  ngOnInit(): void {
    // If updating an existing project, skip to step 2
    if (this.data.project) {
      this.currentStep = 2;
      // Get module from localStorage or default to statistical
      const storedModule = localStorage.getItem(`project_${this.data.project.id}_module`);
      if (storedModule && ['statistical', 'bibliometric', 'power'].includes(storedModule)) {
        this.selectedProjectModule = storedModule as 'statistical' | 'bibliometric' | 'power';
      } else {
        this.selectedProjectModule = 'statistical'; // Default for existing projects
      }
    }

    this.updateExampleUrl();
    this.form = new FormGroup({
      name: new FormControl(this.data.project ? this.data.project.name : '')
    });
    this.dialogRef.disableClose = true;
    this.dialogRef.backdropClick.subscribe(() => {
      this.checkChanges();
    });
  }

  // Remove the mapping methods since we're not using project_type for modules anymore

  // Step management methods
  selectProjectModule(module: 'statistical' | 'bibliometric' | 'power'): void { // Renamed from selectProjectType
    this.selectedProjectModule = module;
  }

  nextStep(): void {
    if (this.currentStep === 1 && this.selectedProjectModule) {
      this.currentStep = 2;
      this.updateExampleUrl();
    }
  }

  previousStep(): void {
    if (this.currentStep === 2) {
      this.currentStep = 1;
      // Reset file when going back
      this.resetFileInput();
    }
  }

  // Project module helper methods (renamed from project type methods)
  getProjectModuleClass(): string {
    switch (this.selectedProjectModule) {
      case 'statistical': return 'bg-blue-50 border border-blue-200';
      case 'bibliometric': return 'bg-green-50 border border-green-200';
      case 'power': return 'bg-purple-50 border border-purple-200';
      default: return 'bg-gray-50 border border-gray-200';
    }
  }

  getProjectModuleIcon(): string {
    switch (this.selectedProjectModule) {
      case 'statistical': return 'lucideChartNoAxesCombined';
      case 'bibliometric': return 'lucideBookOpen';
      case 'power': return 'lucideZap';
      default: return 'lucideFolderPlus';
    }
  }

  getProjectModuleTitle(): string {
    switch (this.selectedProjectModule) {
      case 'statistical': return 'İstatistiksel Veri Analizi';
      case 'bibliometric': return 'Bibliyometrik Analiz';
      case 'power': return 'Power Analizi';
      default: return '';
    }
  }

  getProjectModuleDescription(): string {
    switch (this.selectedProjectModule) {
      case 'statistical': return 'Excel verilerinizi yükleyerek kapsamlı istatistiksel analizler gerçekleştirin';
      case 'bibliometric': return 'Web of Science verilerinizi analiz ederek bibliyometrik raporlar oluşturun';
      case 'power': return 'Araştırmanızın güç analizini yaparak örneklem boyutunu belirleyin';
      default: return '';
    }
  }

  // File handling helper methods
  getDatasetLabel(): string {
    switch (this.selectedProjectModule) {
      case 'statistical': return 'Veri Seti';
      case 'bibliometric': return 'WoS Dosyası (TXT Formatında)';
      default: return 'Veri Seti';
    }
  }

  getUploadText(): string {
    switch (this.selectedProjectModule) {
      case 'statistical': return 'Veri seti yükle';
      case 'bibliometric': return 'WoS TXT dosyası yükle';
      default: return 'Veri seti yükle';
    }
  }

  getFileTypeText(): string {
    switch (this.selectedProjectModule) {
      case 'statistical': return 'Sadece Excel dosyaları';
      case 'bibliometric': return 'Sadece TXT formatında WoS dosyaları';
      default: return 'Sadece Excel dosyaları';
    }
  }

  getAcceptedFileTypes(): string {
    switch (this.selectedProjectModule) {
      case 'statistical': return '.xlsx';
      case 'bibliometric': return '.txt';
      default: return '.xlsx';
    }
  }

  getFileIcon(): string {
    switch (this.selectedProjectModule) {
      case 'statistical': return 'lucideFileSpreadsheet';
      case 'bibliometric': return 'lucideFileText';
      default: return 'lucideFileSpreadsheet';
    }
  }

  // Example section helper methods
  getExampleSectionClass(): string {
    switch (this.selectedProjectModule) {
      case 'statistical': return 'bg-brand-blue-100';
      case 'bibliometric': return 'bg-green-100';
      case 'power': return 'bg-purple-100';
      default: return 'bg-brand-blue-100';
    }
  }

  getExampleIconName(): string {
    switch (this.selectedProjectModule) {
      case 'statistical': return 'lucideFileSpreadsheet';
      case 'bibliometric': return 'lucideFileText';
      case 'power': return 'lucideZap';
      default: return 'lucideFileSpreadsheet';
    }
  }

  getExampleTitle(): string {
    switch (this.selectedProjectModule) {
      case 'statistical': return 'Tablo Örneği';
      case 'bibliometric': return 'WoS TXT Örneği';
      case 'power': return 'Power Analizi Kılavuzu';
      default: return 'Tablo Örneği';
    }
  }

  getExampleInfo(): string {
    switch (this.selectedProjectModule) {
      case 'statistical': return 'Örnek Excel dosyasını inceleyebilirsiniz';
      case 'bibliometric': return 'Örnek WoS TXT dosyasını inceleyebilirsiniz';
      case 'power': return 'Power analizi hakkında detaylı bilgi alın';
      default: return 'Örnek Excel dosyasını inceleyebilirsiniz';
    }
  }

  updateExampleUrl(): void {
    if (this.selectedProjectModule && this.exampleUrls[this.selectedProjectModule]) {
      const lang = this.transloco.getActiveLang();
      this.example_url = this.exampleUrls[this.selectedProjectModule][lang] || this.exampleUrls[this.selectedProjectModule]['en'];
    }
  }

  getCurrentExampleUrl(): string {
    return this.example_url;
  }

  // File handling methods
  resetFileInput(): void {
    this.file = null;
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
    this.isFileUploaded = null;
    this.s3_url = null;
    this.fileName = '';
    this.actualFileName = '';
  }

  async handleFileInput(event): Promise<void> {
    this.isFileUploaded = false;
    const selectedFile = event.target.files[0];

    if (!selectedFile) {
      return;
    }

    // Handle different file types based on project module
    switch (this.selectedProjectModule) {
      case 'statistical':
        await this.handleStatisticalFile(selectedFile);
        break;
      case 'bibliometric':
        await this.handleBibliometricFile(selectedFile);
        break;
    }
  }

  private async handleStatisticalFile(selectedFile: File): Promise<void> {
    this.actualFileName = await this.excelService.readDatasetName(selectedFile);
    try {
      // File validation
      const fileValidation = this.d.validateFile(selectedFile);
      if (!fileValidation.isValid) {
        fileValidation.errors.forEach(error => {
          this.snotifyService.error(error.message);
        });
        this.resetFileInput();
        return;
      }

      // Excel content validation
      const data = await this.d.parseExcelDataView(selectedFile);
      const contentValidation = this.d.validateDataContent(data);
      if (!contentValidation.isValid) {
        contentValidation.errors.forEach(error => {
          this.snotifyService.error(error.message);
        });
        this.resetFileInput();
        return;
      }

      // Process Excel file
      const processedData = await this.excelService.readExcelFile(selectedFile);
      const processedBlob = await this.excelService.writeExcelFile(processedData, selectedFile.name);

      if (this.file != null && this.file != selectedFile) {
        // Delete existing file
        this.d.getDeletePresignedUrl(this.fileName).subscribe({
          next: (data) => {
            this.d.deleteToS3(data.url).subscribe({
              next: async () => {
                await this.uploadProcessedFile(selectedFile, processedBlob);
              },
              error: () => {
                this.isFileUploaded = null;
              }
            });
          },
          error: () => {
            this.isFileUploaded = null;
          }
        });
      } else {
        await this.uploadProcessedFile(selectedFile, processedBlob);
      }
    } catch (error) {
      console.error(error);
      this.snotifyService.error('Dosya işleme hatası');
      this.isFileUploaded = null;
      this.resetFileInput();
    }
  }

  private async handleBibliometricFile(selectedFile: File): Promise<void> {
    // Validate that it's a TXT file
    if (!selectedFile.name.toLowerCase().endsWith('.txt')) {
      this.snotifyService.error('Lütfen TXT formatında WoS dosyası yükleyin.');
      this.resetFileInput();
      return;
    }

    // For now, just set the file without processing
    // TODO: Add bibliometric file validation and processing
    this.file = selectedFile;
    this.actualFileName = selectedFile.name;
    this.isFileUploaded = true;
    this.snotifyService.info('WoS TXT dosyası yüklendi. Bu özellik henüz geliştirme aşamasındadır.');
  }

  private async uploadProcessedFile(originalFile: File, processedBlob: Blob): Promise<void> {
    this.file = originalFile;
    this.fileName = this.updateFileName(this.file.name);

    this.d.getUploadPresignedUrl(this.fileName, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
      .subscribe({
        next: (data: any) => {
          const processedFile = new File([processedBlob], this.fileName, {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          });

          this.d.uploadToS3(data.url, processedFile).subscribe({
            next: () => {
              this.isFileUploaded = true;
              this.s3_url = `https://istabot-development.s3.eu-central-1.amazonaws.com/${this.fileName}`;
            },
            error: () => {
              this.isFileUploaded = null;
            }
          });
        },
        error: () => {
          this.isFileUploaded = null;
        }
      });
  }

  // Project creation methods
  async createProject() {
    if (this.isLoading) return;

    this.submitted = true;
    if (this.form.invalid) {
      this.isLoading = false;
      return;
    }

    // File validation check for statistical and bibliometric projects
    if (this.selectedProjectModule === 'statistical') {
      if (!this.file || !this.s3_url || !this.isFileUploaded) {
        this.isFileUploaded = null;
        this.s3_url = null;
      }
    }

    // For bibliometric projects, file is optional
    if (this.selectedProjectModule === 'bibliometric' && this.file && !this.isFileUploaded) {
      this.snotifyService.error('Dosya yükleme hatası. Lütfen tekrar deneyin.');
      this.isLoading = false;
      return;
    }

    this.isLoading = true;
    if (this.isSuccessFromistabot == null) {
      const status = Observable.create((observer) => {
        // Create project with normal type (we handle modules separately)
        const createProjectObservable = this.projectService.createProject({
          name: this.form.value.name,
          project_type: 'normal' // Always use normal for now, module is stored separately
        });

        createProjectObservable.subscribe((data) => {
          this.projectName = data.name;
          this.protectId = data.id;

          // Store the selected module in localStorage
          if (this.selectedProjectModule) {
            localStorage.setItem(`project_${data.id}_module`, this.selectedProjectModule);
          }

          // Handle file upload based on project module
          if (this.selectedProjectModule === 'statistical' && this.isFileUploaded === true && this.file && this.s3_url) {
            // Statistical analysis flow
            this.d.addDatasetToProject(this.s3_url, this.actualFileName, data.id).subscribe((data) => {
              this.datasetId = data.id;
              observer.next({
                title: this.transloco.translate('shared.create_project.success'),
                body: this.transloco.translate('shared.create_project.diagnose_message'),
                config: {
                  closeOnClick: true,
                  timeout: 1500,
                  showProgressBar: true,
                },
              });
              observer.complete();
              this.showDiagnose(this.file);
            }, (error) => {
              this.handleProjectCreationError(error, observer, data.id);
            });
          } else if (this.selectedProjectModule === 'bibliometric' && this.isFileUploaded === true && this.file) {
            // Bibliometric analysis flow
            // TODO: Implement bibliometric file upload and processing
            observer.next({
              title: this.transloco.translate('shared.create_project.success'),
              body: 'Bibliyometrik analiz projesi oluşturuldu. Bu özellik geliştirme aşamasındadır.',
              config: {
                closeOnClick: true,
                timeout: 1500,
                showProgressBar: true,
              },
            });
            observer.complete();
            setTimeout(() => this.dialogRef.close({ saved: true, projectId: this.protectId }), 300);
          } else {
            // For power analysis or when no file is uploaded
            const message = this.selectedProjectModule === 'power'
              ? 'Power analizi projesi oluşturuldu.'
              : this.transloco.translate('shared.create_project.create_message');

            observer.next({
              title: this.transloco.translate('shared.create_project.success'),
              body: message,
              config: {
                closeOnClick: true,
                timeout: 1500,
                showProgressBar: true,
              },
            });
            observer.complete();
            setTimeout(() => this.dialogRef.close({ saved: true, projectId: this.protectId }), 300);
          }
        }, (error) => {
          this.isLoading = false;
          observer.error({
            title: this.transloco.translate('shared.create_project.error'),
            body: error.error && error.error.error ? error.error.error : this.transloco.translate('shared.create_project.general_error'),
            config: {
              closeOnClick: true,
              timeout: 3000,
              showProgressBar: true,
            },
          });
        });
      });
      this.snotifyService.async(this.transloco.translate('shared.create_project.project_creating'), status);
    } else {
      this.updateProject();
      this.isSuccessFromistabot = null;
    }
  }

  updateProject() {
    if (this.isLoading) return;

    this.isLoading = true;
    var tmp;
    const status = Observable.create((observer) => {
      if (this.data.project ? this.form.value.name != this.data.project.name : this.form.value.name != this.projectName) {
        this.projectService.updateProjectName(this.data.project ? this.data.project.id : this.protectId, this.form.value.name).subscribe((data) => {
          observer.next({
            title: this.transloco.translate('shared.create_project.success'),
            body: this.transloco.translate('shared.create_project.update_message'),
            config: {
              closeOnClick: true,
              timeout: 1500,
              showProgressBar: true,
            },
          });
          observer.complete();
        });
      }
      if (this.isFileUploaded) {
        this.d.addDatasetToProject(this.s3_url, this.fileName, this.data.project ? this.data.project.id : this.protectId).subscribe((data) => {
          tmp = data;
          this.projectService.getProjectById(this.data.project.id).subscribe((data) => {
            observer.next({
              title: this.transloco.translate('shared.create_project.success'),
              body: this.transloco.translate('shared.create_project.diagnose_message'),
              config: {
                closeOnClick: true,
                timeout: 1500,
                showProgressBar: true,
              },
            });
            observer.complete();
            this.datasetId = tmp.id;
            this.showDiagnose(this.file);
          });
        }, (error) => {
          let errorMessage = '';
          if (error.code) {
            errorMessage = error.code;
          } else if (error?.error?.code) {
            errorMessage = error.error.code;
          }
          observer.error({
            title: this.transloco.translate('shared.create_project.error'),
            config: {
              closeOnClick: true,
              timeout: 3000,
              showProgressBar: true,
            },
          });
          this.d.getDeletePresignedUrl(this.fileName).subscribe({
            next: (data) => {
              this.d.deleteToS3(data.url).subscribe({
                next: () => {
                  this.resetFileInput();
                },
                error: () => {
                  this.resetFileInput();
                }
              });
            },
            error: () => {
              this.resetFileInput();
            }
          });
        });
      } else {
        this.hideModal();
      }
    });
    this.snotifyService.async(this.transloco.translate('shared.create_project.project_creating'), status);
  }

  async showDiagnose(file: File) {
    try {
      const diagnoseData = await this.diagnoseHelper.prepareDiagnoseData(
        file,
        this.s3_url,
        this.data.project ? this.data.project.id : this.protectId,
        this.datasetId?.toString(),
      );

      const dialog = this.dialog.open(DiagnoseComponent, {
        data: {
          ...diagnoseData,
          fromCreateProject: true,
        },
        width: '100%'
      });

      dialog.closed.subscribe(async (result: { saved: boolean, projectId: number }) => {
        if (result) {
          this.isDiagnoseOpen = true;
          this.animationState = 'out';
          setTimeout(() => this.dialogRef.close({
            ...result,
            isDiagnoseOpen: this.isDiagnoseOpen
          }), 300);
        }
      });
    } catch (error) {
      this.snotifyService.error('Error processing file');
    }
  }

  // Helper methods
  private handleProjectCreationError(error: any, observer: any, projectId: number): void {
    let errorMessage = '';
    if (error.code) {
      errorMessage = error.code;
    } else if (error?.error?.code) {
      errorMessage = error.error.code;
    }

    this.projectService.deleteProject(projectId).subscribe(() => {
      this.isSuccessFromistabot = null;
      if (this.fileName) {
        this.d.getDeletePresignedUrl(this.fileName).subscribe({
          next: (data) => {
            this.d.deleteToS3(data.url).subscribe({
              next: () => {
                this.resetFileInput();
                this.showError(observer, error, errorMessage);
              },
              error: () => {
                this.resetFileInput();
                this.showError(observer, error, errorMessage);
              }
            });
          },
          error: () => {
            this.resetFileInput();
            this.showError(observer, error, errorMessage);
          }
        });
      } else {
        this.resetFileInput();
        this.showError(observer, error, errorMessage);
      }
    });
  }

  private showError(observer: any, error: any, errorMessage: string): void {
    observer.error({
      title: this.transloco.translate('shared.create_project.error'),
      body: error.error.error ? error.error.error + ' ' + this.transloco.translate('shared.create_project.errors.' + errorMessage) : this.transloco.translate('shared.create_project.errors.' + errorMessage),
      config: {
        closeOnClick: true,
        timeout: 3000,
        showProgressBar: true,
      },
    });
  }

  isDemoProject(): boolean {
    return this.data?.project?.project_type === 'demo';
  }

  canUpdateDataset(): boolean {
    if (this.isDemoProject()) {
      return false;
    }
    return !this.project?.datasets?.length ||
      (!this.project.datasets[0].diagnosed_s3_url && !this.hasReports);
  }

  updateFileName(name: string): string {
    const fileName = name;
    const extension = fileName.substring(fileName.lastIndexOf('.'));
    const fileNameWithProjectId = fileName.replace(extension, '') + new Date().toISOString();
    return this.slugify(fileNameWithProjectId) + extension;
  }

  slugify(text: string): string {
    const turkishMap = {
      'ş': 's', 'Ş': 'S', 'ç': 'c', 'Ç': 'C', 'ğ': 'g', 'Ğ': 'G',
      'ü': 'u', 'Ü': 'U', 'ö': 'o', 'Ö': 'O', 'ı': 'i', 'İ': 'I'
    };

    return text
      .toLowerCase()
      .replace(/[şŞçÇğĞüÜöÖıİ]/g, (match) => turkishMap[match])
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-');
  }

  openHowToCreateProject() {
    this.dialog.open(VideoEmbedComponent, {
      data: {
        from: 'create-project'
      },
    });
  }

  checkChanges() {
    const hasFileInputValue = this.fileInput && this.fileInput.nativeElement &&
      this.fileInput.nativeElement.value !== '';

    if (this.form.dirty || hasFileInputValue || this.file) {
      const dialogRef = this.dialog.open(ConfirmComponent, {
        data: {
          title: this.transloco.translate('shared.confirm.create_project.title'),
          content: this.transloco.translate('shared.confirm.create_project.content'),
          confirm: this.transloco.translate('shared.confirm.create_project.confirm'),
          cancel: this.transloco.translate('shared.confirm.create_project.cancel')
        },
      });
      dialogRef.closed.subscribe((result) => {
        if (result) {
          if (this.file && this.fileName) {
            this.d.getDeletePresignedUrl(this.fileName).subscribe({
              next: (data) => {
                this.d.deleteToS3(data.url).subscribe({
                  next: () => {
                    this.hideModal(result);
                  },
                  error: () => {
                    this.hideModal(result);
                  }
                });
              },
              error: () => {
                this.hideModal(result);
              }
            });
          } else {
            this.hideModal(result);
          }
        }
      });
    } else {
      this.hideModal();
    }
  }

  hideModal(result?) {
    this.ngZone.run(() => {
      this.animationState = 'out';
      if (result) {
        setTimeout(() => this.dialogRef.close(), 300);
      } else {
        setTimeout(() => this.dialogRef.close(result), 300);
      }
    });
  }

  // Event listeners
  @HostListener('window:keyup.esc') onKeyUp() {
    this.checkChanges();
  }

  @HostListener("window:beforeunload", ["$event"]) unloadHandler(event: Event) {
    this.checkChanges();
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = true;
  }

  onDragLeave(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = false;
  }

  onDrop(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = false;

    const files = event.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];

      // Check file type based on selected project module
      let isValidFileType = false;
      switch (this.selectedProjectModule) {
        case 'statistical':
          isValidFileType = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          break;
        case 'bibliometric':
          isValidFileType = file.type === 'text/plain' || file.name.endsWith('.txt');
          break;
      }

      if (isValidFileType) {
        const event = { target: { files: [file] } };
        this.handleFileInput(event);
      }
    }
  }

  downloadExample() {
    window.open(this.example_url, '_blank');
  }
}