<!-- create-project.component.html - Updated with module concept -->
<div *transloco="let t ; read: 'shared.create_project'">
    <div class="overflow-hidden bg-white shadow-xl rounded-3xl">
        <!-- Header -->
        <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200 ">
            <div class="text-white/0">
                <ng-icon name="lucideX" class="text-2xl"></ng-icon>
            </div>
            <div class="flex items-center gap-2 text-neutral-950">
                <ng-icon name="lucideFolderPlus" class="text-2xl"></ng-icon>
                <h3 class="text-2xl font-medium">{{data.project!=null ? t('update_project') : t('new_project')}}</h3>
            </div>
            <button class="pt-2 text-gray-600 hover:text-neutral-950" (click)="checkChanges()">
                <ng-icon name="lucideX" class="text-2xl"></ng-icon>
            </button>
        </div>

        <!-- <PERSON><PERSON><PERSON><PERSON> -->
        <div class="w-full pt-4 px-7">
            <div class="flex justify-between mb-2">
                <div class="flex flex-col items-center">
                    <div [class]="'w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 ' + 
                            (currentStep > 1 ? 'bg-brand-blue-500' : 
                            currentStep === 1 ? 'border-2 border-brand-blue-500 bg-white' :
                            'border-2 border-gray-200 bg-white')">
                        <ng-container *ngIf="currentStep > 1 else stepNumber1">
                            <ng-icon name="lucideCheck" class="w-6 h-6 text-white"></ng-icon>
                        </ng-container>
                        <ng-template #stepNumber1>
                            <span [class]="'text-lg ' + (currentStep === 1 ? 'text-brand-blue-500' : 'text-gray-400')">
                                1
                            </span>
                        </ng-template>
                    </div>
                    <span class="text-nowrap" [class]="'text-sm mt-2 font-medium transition-all duration-300 ' +
                            (currentStep === 1 ? 'text-brand-blue-500' : 'text-gray-400')">
                        Analiz Modülü
                    </span>
                </div>

                <div class="w-full h-2 mt-4 bg-gray-200 rounded-full">
                    <div class="h-2 transition-all duration-500 rounded-full bg-brand-blue-500"
                        [style.width.%]="((currentStep - 1) / 1) * 100">
                    </div>
                </div>

                <div class="flex flex-col items-center">
                    <div [class]="'w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 ' + 
                            (currentStep > 2 ? 'bg-brand-blue-500' : 
                            currentStep === 2 ? 'border-2 border-brand-blue-500 bg-white' :
                            'border-2 border-gray-200 bg-white')">
                        <ng-container *ngIf="currentStep > 2 else stepNumber2">
                            <ng-icon name="lucideCheck" class="w-6 h-6 text-white"></ng-icon>
                        </ng-container>
                        <ng-template #stepNumber2>
                            <span [class]="'text-lg ' + (currentStep === 2 ? 'text-brand-blue-500' : 'text-gray-400')">
                                2
                            </span>
                        </ng-template>
                    </div>
                    <span class="text-nowrap" [class]="'text-sm mt-2 font-medium transition-all duration-300 ' +
                            (currentStep === 2 ? 'text-brand-blue-500' : 'text-gray-400')">
                        Proje Detayları
                    </span>
                </div>
            </div>
        </div>

        <!-- Step 1: Project Module Selection -->
        <div *ngIf="currentStep === 1" class="p-6">
            <div class="space-y-4">
                <h4 class="mb-2 text-lg font-medium text-neutral-950">Analiz modülünü seçin</h4>

                <!-- Statistical Data Analysis -->
                <button (click)="selectProjectModule('statistical')"
                    [ngClass]="selectedProjectModule === 'statistical' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'"
                    class="w-full p-6 text-left transition-all duration-200 border-2 rounded-3xl">
                    <div class="flex items-center gap-4">
                        <div class="p-3 bg-blue-100 size-12 rounded-xl">
                            <ng-icon name="lucideChartNoAxesCombined" class="text-2xl text-blue-600"></ng-icon>
                        </div>
                        <div class="flex-1">
                            <h5 class="mb-2 text-lg font-medium text-neutral-950">İstatistiksel Veri Analizi</h5>
                            <p class="text-sm text-gray-600">Excel verilerinizi yükleyerek kapsamlı istatistiksel
                                analizler gerçekleştirin</p>
                        </div>
                        <div *ngIf="selectedProjectModule === 'statistical'" class="text-blue-500">
                            <ng-icon name="lucideCheck" class="text-2xl"></ng-icon>
                        </div>
                    </div>
                </button>

                <!-- Bibliometric Analysis -->
                <button (click)="selectProjectModule('bibliometric')"
                    [ngClass]="selectedProjectModule === 'bibliometric' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'"
                    class="w-full p-6 text-left transition-all duration-200 border-2 rounded-3xl">
                    <div class="flex items-center gap-4">
                        <div class="p-3 bg-green-100 size-12 rounded-xl">
                            <ng-icon name="lucideBookOpen" class="text-2xl text-green-600"></ng-icon>
                        </div>
                        <div class="flex-1">
                            <h5 class="mb-2 text-lg font-medium text-neutral-950">Bibliyometrik Analiz</h5>
                            <p class="text-sm text-gray-600">Web of Science verilerinizi analiz ederek bibliyometrik
                                raporlar oluşturun</p>
                        </div>
                        <div *ngIf="selectedProjectModule === 'bibliometric'" class="text-blue-500">
                            <ng-icon name="lucideCheck" class="text-2xl"></ng-icon>
                        </div>
                    </div>
                </button>

                <!-- Power Analysis -->
                <button (click)="selectProjectModule('power')"
                    [ngClass]="selectedProjectModule === 'power' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'"
                    class="w-full p-6 text-left transition-all duration-200 border-2 rounded-3xl">
                    <div class="flex items-center gap-4">
                        <div class="p-3 bg-purple-100 size-12 rounded-xl">
                            <ng-icon name="lucideZap" class="text-2xl text-purple-600"></ng-icon>
                        </div>
                        <div class="flex-1">
                            <h5 class="mb-2 text-lg font-medium text-neutral-950">Power Analizi</h5>
                            <p class="text-sm text-gray-600">Araştırmanızın güç analizini yaparak örneklem boyutunu
                                belirleyin</p>
                        </div>
                        <div *ngIf="selectedProjectModule === 'power'" class="text-blue-500">
                            <ng-icon name="lucideCheck" class="text-2xl"></ng-icon>
                        </div>
                    </div>
                </button>
            </div>

            <!-- Step 1 Footer -->
            <div class="flex justify-between mt-8">
                <button type="button" class="text-blue-button" (click)="openHowToCreateProject()">
                    <ng-icon name="lucidePlay" class="text-xl"></ng-icon>
                    <span>Proje Nasıl Oluşturulur?</span>
                </button>
                <button [disabled]="!selectedProjectModule" (click)="nextStep()"
                    class="inline-flex items-center gap-2 disabled:cursor-not-allowed primary-blue-button disabled:opacity-50">
                    <span>Devam Et</span>
                    <ng-icon name="lucideChevronRight" class="text-xl"></ng-icon>
                </button>
            </div>
        </div>

        <!-- Step 2: Project Details -->
        <div *ngIf="currentStep === 2">
            <form [formGroup]="form" (submit)="data.project == null ? createProject(): updateProject()">
                <div class="p-6 space-y-6">
                    <!-- Selected Project Module Info -->
                    <div class="flex items-center gap-3 p-4 rounded-3xl" [ngClass]="getProjectModuleClass()">
                        <ng-icon [name]="getProjectModuleIcon()" class="text-2xl"></ng-icon>
                        <div>
                            <p class="font-medium text-neutral-950">{{getProjectModuleTitle()}}</p>
                            <p class="text-sm text-gray-600">{{getProjectModuleDescription()}}</p>
                        </div>
                        <button type="button" (click)="previousStep()"
                            class="ml-auto text-sm text-blue-600 hover:text-blue-800">
                            Değiştir
                        </button>
                    </div>

                    <!-- Project Name -->
                    <div>
                        <label class="block mb-2 text-xl font-medium text-neutral-950">Proje Adı</label>
                        <input required type="text" formControlName="name" placeholder="Proje adını girin"
                            class="w-full p-3 border border-gray-200 rounded-3xl focus:ring-2 focus:ring-blue-100 focus:border-blue-500">
                        <div *ngIf="submitted && form.get('name').hasError('required')"
                            class="mt-1 text-xs text-red-500">
                            Proje adı gereklidir
                        </div>
                    </div>

                    <!-- Dataset Upload - Hidden for power analysis -->
                    <div *ngIf="selectedProjectModule !== 'power'">
                        <label class="block mb-2 text-xl font-medium text-neutral-950">
                            {{getDatasetLabel()}}
                            <span class="text-base text-gray-700">(İsteğe bağlı)</span>
                        </label>

                        <!-- Show Current File If Exists -->
                        <div *ngIf="file"
                            class="flex items-center justify-between p-3 mb-3 border border-blue-200 rounded-3xl bg-blue-50">
                            <div class="flex items-center gap-2">
                                <ng-icon [name]="getFileIcon()" class="text-xl text-brand-blue-400"></ng-icon>
                                <span class="max-w-xs text-sm truncate text-neutral-950">{{file.name}}</span>
                            </div>
                            <button type="button" class="p-1 text-gray-400 hover:text-red-500"
                                (click)="resetFileInput()">
                                <ng-icon name="lucideTrash2" class="text-xl"></ng-icon>
                            </button>
                        </div>

                        <!-- Upload Area -->
                        <label for="dataset" [ngClass]="{'border-brand-blue-500': isDragging}" *ngIf="!file"
                            class="block p-4 transition-colors border-2 border-gray-200 border-dashed cursor-pointer group rounded-3xl hover:border-brand-blue-500"
                            (dragover)="onDragOver($event)" (dragleave)="onDragLeave($event)" (drop)="onDrop($event)">
                            <div class="flex flex-col items-center gap-2">
                                <ng-icon name="lucideCloudUpload"
                                    class="text-xl text-gray-400 group-hover:text-brand-blue-500"></ng-icon>
                                <div class="text-sm text-gray-500">
                                    <span class="text-brand-blue-400 hover:underline">{{getUploadText()}}</span>
                                    {{t('or_drag_drop')}}
                                </div>
                                <div class="text-xs text-gray-400">
                                    {{getFileTypeText()}}
                                </div>
                            </div>
                            <input #fileInput (change)="handleFileInput($event)" type="file" id="dataset"
                                [accept]="getAcceptedFileTypes()" class="hidden">
                        </label>
                    </div>

                    <!-- Example Section -->
                    <div *ngIf="selectedProjectModule !== 'power'"
                        class="flex items-center justify-between gap-2 p-4 rounded-3xl"
                        [ngClass]="getExampleSectionClass()">
                        <div class="flex items-center gap-2">
                            <ng-icon [name]="getExampleIconName()" class="text-2xl text-gray-600"></ng-icon>
                            <div>
                                <p class="text-sm font-medium text-neutral-950">{{getExampleTitle()}}</p>
                                <p class="text-xs text-gray-500">{{getExampleInfo()}}</p>
                            </div>
                        </div>
                        <a [href]="getCurrentExampleUrl()" class="secondary-blue-button"
                            (click)="$event.stopPropagation()" target="_blank">
                            <ng-icon name="lucideDownload" class="text-xl"></ng-icon>
                            İndir
                        </a>
                    </div>
                </div>

                <!-- Footer -->
                <div class="flex items-center justify-between gap-3 px-6 pb-4">
                    <div class="flex items-center gap-4">
                        <button class="secondary-blue-button" (click)="previousStep()">
                            <ng-icon name="lucideChevronLeft" class="text-xl"></ng-icon>
                            <span>Geri</span>
                        </button>
                    </div>

                    <button type="submit"
                        [disabled]="form.invalid || (selectedProjectModule !== 'power' && isFileUploaded == false) || isLoading"
                        class="inline-flex items-center gap-2 disabled:cursor-not-allowed primary-blue-button disabled:opacity-50">
                        <ng-icon *ngIf="!isLoading" name="lucidePlus" class="text-xl"></ng-icon>
                        <ng-icon *ngIf="isLoading" name="lucideLoader" class="text-xl animate-spin"></ng-icon>
                        {{data.project!=null ? (isLoading ? 'Güncelleniyor' : 'Güncelle') : (isLoading ? 'Oluşturuluyor'
                        : 'Oluştur')}}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>