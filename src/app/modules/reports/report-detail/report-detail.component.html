<div class="flex flex-col h-full report-detail-container" *transloco="let t; read: 'analyses'" [@opacity]
  [ngClass]="{'report-fullscreen': isFullscreen}">

  <!-- Skeleton Loader -->
  <div *ngIf="isLoading" class="flex flex-col w-full h-full gap-4 p-4 animate-pulse">
    <!-- Header Skeleton -->
    <div class="flex items-center justify-between gap-4 mb-4">
      <div class="flex items-start gap-2">
        <div class="w-10 h-10 bg-gray-200 rounded-full"></div>
        <div class="flex flex-col gap-2">
          <div class="w-48 h-6 bg-gray-200 rounded-3xl"></div>
          <div class="w-32 h-4 bg-gray-200 rounded-3xl"></div>
        </div>
      </div>
      <div class="flex ">
        <div class="w-10 h-10 bg-gray-200 rounded-full rounded-r-none"></div>
        <div class="w-10 h-10 bg-gray-200 "></div>
        <div class="w-10 h-10 bg-gray-200 "></div>
        <div class="w-10 h-10 bg-gray-200 rounded-full rounded-l-none"></div>
      </div>
    </div>

    <!-- Description Skeleton -->
    <div class="w-full h-16 mb-4 bg-gray-200 rounded-3xl"></div>

    <!-- Content Skeleton -->
    <div class="flex items-start w-full gap-4 grow">
      <!-- Navigation Tree Skeleton -->
      <div class="h-full p-4 bg-gray-200 rounded-3xl w-52 shrink-0"></div>

      <!-- Report Content Skeleton -->
      <div class="flex flex-col w-full gap-4 grow">
        <!-- Table Skeleton -->
        <div class="w-full h-40 bg-gray-200 rounded-3xl"></div>

        <!-- Interpretation Skeleton -->
        <div class="flex flex-col gap-2">
          <div class="w-full h-4 bg-gray-200 rounded-3xl"></div>
          <div class="w-full h-4 bg-gray-200 rounded-3xl"></div>
          <div class="w-3/4 h-4 bg-gray-200 rounded-3xl"></div>
        </div>

        <!-- Second Table Skeleton -->
        <div class="w-full h-40 bg-gray-200 rounded-3xl"></div>

        <!-- Second Interpretation Skeleton -->
        <div class="flex flex-col gap-2">
          <div class="w-full h-4 bg-gray-200 rounded-3xl"></div>
          <div class="w-full h-4 bg-gray-200 rounded-3xl"></div>
          <div class="w-2/3 h-4 bg-gray-200 rounded-3xl"></div>
        </div>
      </div>
    </div>
  </div>


  <!-- Actual Content -->
  <div *ngIf="report && !isLoading" class="flex items-center justify-between flex-none gap-4 pt-1 mb-4 bg-neutral-50">
    <div class="flex items-start gap-2 ">

      <button (click)="goBack()" matTooltip="Geri Dön"
        class="flex items-center justify-center p-3 font-bold text-neutral-950 hover:bg-neutral-100">
        <ng-icon name="lucideArrowLeft" class="text-xl"></ng-icon>
      </button>

      <div class="flex flex-col items-start gap-1 ">
        <div class="flex items-center gap-2 truncate">
          <img [src]="reportContent.icon" alt=""
            [ngClass]="{' scale-150':reportContent.code == 'descriptive','': reportContent.code!='descriptive'}"
            class="size-8">

          <!-- Başlık düzenleme alanı -->
          <h1 class="max-w-xl text-2xl font-bold truncate">{{report.title_with_code}}</h1>
        </div>
        <p class="relative flex items-center gap-1 text-xs text-neutral-600">
          <span>
            #{{report.id}}
          </span>
          <ng-icon name="lucideClock4" class=""></ng-icon>
          <span class="" matTooltipPosition="below"
            [matTooltip]="reportContent.created_at.full_time | date:'dd/MM/yyyy HH:mm'">
            {{
            reportContent.lang =='tr'? reportContent.created_at.tr : reportContent.created_at.en}}
          </span>
          <img src="assets/icons/istacoin.svg" alt="" class="size-4">
          <span class="relative cursor-help" *ngIf="reportContent.credit_usage.credit_usages?.length > 0">
            <span class="underline decoration-dotted" (mouseenter)="showCreditTooltip = true">
              {{reportContent.lang =='tr'? reportContent.credit_usage.tr :reportContent.credit_usage.en}}
            </span>
            <!-- Tooltip for credit usage details -->
            <div *ngIf="showCreditTooltip" (mouseleave)="showCreditTooltip = false"
              class="absolute right-0 top-0 z-[9999] p-3 text-xs bg-white border-2 border-brand-blue-400 shadow-lg w-72 rounded-xl">
              <div class="mb-1 font-semibold">
                {{reportContent.lang =='tr'? 'Kredi Kullanım Detayları' : 'Credit Usage Details'}}
              </div>
              <div class="overflow-y-auto max-h-48">
                <div class="flex flex-col gap-1">
                  <div *ngFor="let usage of reportContent.credit_usage.credit_usages"
                    class="flex justify-between p-1 border-b border-gray-100">
                    <span>{{usage.used_credit}} {{reportContent.lang =='tr'? 'kredi' : 'credit'}}</span>
                    <span class="text-gray-500">{{reportContent.lang =='tr'? usage.formatted_date.tr :
                      usage.formatted_date.en}}</span>
                  </div>
                </div>
              </div>
              <div class="flex justify-between pt-2 mt-1 font-medium border-t border-gray-200">
                <span>{{reportContent.lang =='tr'? 'Toplam' : 'Total'}}</span>
                <span>{{reportContent.credit_usage.total_credits}} {{reportContent.lang =='tr'? 'kredi' :
                  'credit'}}</span>
              </div>
            </div>
          </span>
          <span *ngIf="!reportContent.credit_usage.credit_usages?.length">
            {{reportContent.lang =='tr'? reportContent.credit_usage.tr :reportContent.credit_usage.en}}
          </span>
        </p>
      </div>

    </div>

    <div class="flex">
      <div class="flex gap-1 p-1 bg-white border rounded-r-none border-r-none rounded-3xl">
        <button (click)="openEditReportDialog()"
          class="flex items-center justify-center gap-2 px-1 py-1 pl-2 text-xs font-medium transition-all origin-right border-2 border-transparent rounded-r-lg cursor-pointer hover:border-neutral-300 hover:bg-neutral-100 hover disabled:scale-95 disabled:border-zinc-200 disabled:opacity-50 md:text-base active:scale-95 border-slate-200 text-green-950 group rounded-3xl"
          [matTooltip]="t('edit_report_title')">
          <ng-icon name="lucideSquarePen" class="text-2xl font-semibold"></ng-icon>
        </button>
        <!-- Favorite -->
        <button matTooltip="{{t('add_remove_favorites')}}" (click)="toggleFavorite($event)"
          class="flex items-center justify-center gap-2 px-1 py-1 text-xs font-medium transition-all origin-right border-2 border-transparent cursor-pointer rounded-xl hover:border-neutral-300 hover:bg-neutral-100 hover disabled:scale-95 disabled:border-zinc-200 disabled:opacity-50 md:text-base active:scale-95 border-slate-200 text-green-950 group ">
          <ng-icon [name]="reportContent.favorite ? 'tablerStarFilled' : 'tablerStarOff'" class="text-2xl"
            [class.text-yellow-500]="reportContent.favorite" [class.fill-yellow-500]="reportContent.favorite"
            [class.text-gray-400]="!reportContent.favorite"></ng-icon>
        </button>
        <!-- Maximize/Minimize Toggle -->
        <button [matTooltip]="isFullscreen ? t('exit_fullscreen') : t('maximize_report')" (click)="toggleFullscreen()"
          class="flex items-center justify-center gap-2 px-1 py-1 text-xs font-medium transition-all origin-right border-2 border-transparent cursor-pointer rounded-xl hover:border-neutral-300 hover:bg-neutral-100 hover disabled:scale-95 disabled:border-zinc-200 disabled:opacity-50 md:text-base active:scale-95 border-slate-200 text-green-950 group ">
          <ng-icon [name]="isFullscreen ? 'lucideMinimize' : 'lucideExpand'" class="text-2xl"></ng-icon>
        </button>
        <!--Clone -->
        <button (click)="getReportClone()" matTooltip="{{t('clone_report')}}"
          class="flex items-center justify-center gap-2 px-1 py-1 text-xs font-medium transition-all origin-right border-2 border-transparent cursor-pointer rounded-xl hover:border-neutral-300 hover:bg-neutral-100 hover disabled:scale-95 disabled:border-zinc-200 disabled:opacity-50 md:text-base active:scale-95 border-slate-200 text-green-950 group ">
          <ng-icon name="lucideRefreshCcw" class="text-2xl"></ng-icon>
        </button>
        <!-- Delete -->
        <button (click)="deleteReport()" matTooltip="{{t('delete_report')}}"
          class="flex items-center justify-center gap-2 px-1 py-1 text-xs font-medium text-red-700 transition-all origin-right border-2 border-transparent cursor-pointer rounded-xl hover:border-status-error-400 hover:bg-status-error-100 hover disabled:scale-95 disabled:border-zinc-200 disabled:opacity-50 md:text-base active:scale-95 border-slate-200 group ">
          <ng-icon name="lucideTrash2" class="text-2xl"></ng-icon>
        </button>
      </div>
      <!-- Actions -->
      <div class="relative">
        <button (click)="toggleReportActions()" name="report_actions"
          [ngClass]="reportContent.showOptions ? 'ring-4 ring-brand-green-200 border-brand-green-500' : ''"
          class="flex items-center justify-center p-3  text-sm font-normal border-l-none transition-all bg-white border  gap-0.5 hover:border-green-600 text-green-950 active:scale-95 group rounded-l-none rounded-3xl hover:border-3 ">
          <ng-icon name="lucideEllipsisVertical" class="text-2xl"></ng-icon>
        </button>
        <div *ngIf="reportContent.showOptions" appClickOutside (clickOutside)="reportContent.showOptions = false"
          [@fadeIn]
          class="absolute right-0 z-40 flex flex-col gap-1 p-3 mt-2 bg-white border-2 border-black shadow-md rounded-3xl">
          <p class="mb-2 text-base font-semibold">
            {{t('options')}}
          </p>
          <div class="flex flex-col gap-2">
            <label class="w-full group peer ">
              <input type="checkbox" class="hidden">
              <div
                class="flex items-center justify-between group-has-[:checked]:border-black w-full gap-2 px-3 py-2 text-xs font-medium transition-all bg-white border shadow cursor-pointer disabled:scale-95 disabled:border-zinc-200 disabled:opacity-50 md:text-sm active:scale-95 border-slate-200 hover:border-black text-green-950 rounded-3xl">
                <div class="flex items-center gap-2 ">
                  <ng-icon name="lucideLanguages" class="text-2xl font-semibold"></ng-icon>
                  {{t('language')}}
                </div>
                <div class="flex items-center gap-2 ">
                  <svg *ngIf="reportContent.lang == 'tr'" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    class="scale-105" viewBox="0 0 16 16" fill="none">
                    <g clip-path="url(#clip0_986_11131)">
                      <path
                        d="M16 12.0004C16 12.4719 15.8127 12.9241 15.4793 13.2575C15.1459 13.5909 14.6937 13.7782 14.2222 13.7782H1.77778C1.30628 13.7782 0.854097 13.5909 0.520699 13.2575C0.187301 12.9241 0 12.4719 0 12.0004V4.00043C0 3.52894 0.187301 3.07675 0.520699 2.74336C0.854097 2.40996 1.30628 2.22266 1.77778 2.22266H14.2222C14.6937 2.22266 15.1459 2.40996 15.4793 2.74336C15.8127 3.07675 16 3.52894 16 4.00043V12.0004Z"
                        fill="#E30917" />
                      <path
                        d="M7.11001 10.6671C6.40277 10.6671 5.72449 10.3861 5.2244 9.88604C4.7243 9.38594 4.44335 8.70767 4.44335 8.00042C4.44335 7.29318 4.7243 6.6149 5.2244 6.1148C5.72449 5.61471 6.40277 5.33376 7.11001 5.33376C7.69224 5.33376 8.23001 5.52264 8.66868 5.83953C8.36284 5.49164 7.98634 5.21295 7.56429 5.02204C7.14224 4.83113 6.68434 4.7324 6.22113 4.73242C5.3544 4.73242 4.52317 5.07673 3.9103 5.6896C3.29743 6.30247 2.95313 7.13369 2.95313 8.00042C2.95307 8.42962 3.03755 8.85462 3.20176 9.25117C3.36597 9.64771 3.60668 10.008 3.91014 10.3115C4.21361 10.615 4.57389 10.8558 4.97041 11.0201C5.36693 11.1843 5.79193 11.2689 6.22113 11.2689C7.19668 11.2689 8.07001 10.8391 8.66868 10.1613C8.2156 10.4903 7.66995 10.6674 7.11001 10.6671ZM8.84913 8.10264L9.93357 8.35242L10.0313 9.46042L10.6038 8.50664L11.6882 8.75598L10.9576 7.91687L11.5296 6.96264L10.506 7.3982L9.77535 6.55864L9.87313 7.66709L8.84913 8.10264Z"
                        fill="#EEEEEE" />
                    </g>
                    <defs>
                      <clipPath id="clip0_986_11131">
                        <rect width="16" height="16" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                  <svg *ngIf="reportContent.lang == 'en'" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    viewBox="0 0 16 12" fill="none" class="sidebar">
                    <g clip-path="url(#clip0_986_11140)">
                      <mask id="mask0_986_11140" style="mask-type: luminance" maskUnits="userSpaceOnUse" x="0" y="0"
                        width="16" height="12">
                        <path d="M0 0H15.995V11.9963H0V0Z" fill="white" />
                      </mask>
                      <g mask="url(#mask0_986_11140)">
                        <path d="M0 0H15.995V11.9963H0V0Z" fill="white" />
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0V11.9963H15.995V0H0Z" fill="#2E42A5" />
                        <mask id="mask1_986_11140" style="mask-type: luminance" maskUnits="userSpaceOnUse" x="0" y="0"
                          width="16" height="12">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0V11.9963H15.995V0H0Z" fill="white" />
                        </mask>
                        <g mask="url(#mask1_986_11140)">
                          <path
                            d="M-1.78125 11.1394L1.73865 12.6284L16.0742 1.61883L17.9311 -0.593476L14.1673 -1.09082L8.32009 3.6532L3.61356 6.8502L-1.78125 11.1394Z"
                            fill="white" />
                          <path
                            d="M-1.30469 12.1826L0.489252 13.0463L17.2595 -0.798828H14.7418L-1.30419 12.1821L-1.30469 12.1826Z"
                            fill="#F50100" />
                          <path
                            d="M17.7748 11.1394L14.2549 12.6284L-0.0805801 1.61883L-1.9375 -0.593476L1.82632 -1.09082L7.6735 3.6532L12.38 6.8502L17.7748 11.1394Z"
                            fill="white" />
                          <path
                            d="M17.6541 11.8882L15.8607 12.7519L8.71993 6.82375L6.60259 6.16195L-2.11719 -0.585938H0.401026L9.1153 6.002L11.4301 6.79576L17.6541 11.8882Z"
                            fill="#F50100" />
                          <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M9.88723 -0.999023H6.11041V3.99941H-0.984375V7.99817H6.11041V12.9966H9.88723V7.99817H17.01V3.99941H9.88723V-0.999023Z"
                            fill="#F50100" />
                          <mask id="mask2_986_11140" style="mask-type: luminance" maskUnits="userSpaceOnUse" x="-1"
                            y="-1" width="19" height="14">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                              d="M9.88723 -0.999023H6.11041V3.99941H-0.984375V7.99817H6.11041V12.9966H9.88723V7.99817H17.01V3.99941H9.88723V-0.999023Z"
                              fill="white" />
                          </mask>
                          <g mask="url(#mask2_986_11140)">
                            <path
                              d="M6.1101 -0.999336V-1.99902H5.11041V-0.999336H6.1101ZM9.88692 -0.999336H10.8866V-1.99902H9.88692V-0.999336ZM6.1101 3.9991V4.99879H7.10978V3.9991H6.1101ZM-0.984687 3.9991V2.99941H-1.98438V3.9991H-0.984687ZM-0.984687 7.99785H-1.98438V8.99754H-0.984687V7.99785ZM6.1101 7.99785H7.10978V6.99817H6.1101V7.99785ZM6.1101 12.9963H5.11041V13.996H6.1101V12.9963ZM9.88692 12.9963V13.996H10.8866V12.9963H9.88692ZM9.88692 7.99785V6.99817H8.88723V7.99785H9.88692ZM17.0097 7.99785V8.99754H18.0094V7.99785H17.0097ZM17.0097 3.9991H18.0094V2.99941H17.0097V3.9991ZM9.88692 3.9991H8.88723V4.99879H9.88692V3.9991ZM6.1101 0.000351787H9.88692V-1.99902H6.1101V0.000351787ZM7.10978 3.9991V-0.999336H5.11041V3.9991H7.10978ZM-0.984687 4.99879H6.1101V2.99941H-0.984687V4.99879ZM0.0150003 7.99785V3.9991H-1.98438V7.99785H0.0150003ZM6.1101 6.99817H-0.984687V8.99754H6.1101V6.99817ZM7.10978 12.9963V7.99785H5.11041V12.9963H7.10978ZM9.88692 11.9966H6.1101V13.996H9.88692V11.9966ZM8.88723 7.99785V12.9963H10.8866V7.99785H8.88723ZM17.0097 6.99817H9.88692V8.99754H17.0097V6.99817ZM16.01 3.9991V7.99785H18.0094V3.9991H16.01ZM9.88692 4.99879H17.0097V2.99941H9.88692V4.99879ZM8.88723 -0.999336V3.9991H10.8866V-0.999336H8.88723Z"
                              fill="white" />
                          </g>
                        </g>
                      </g>
                    </g>
                  </svg>
                  <ng-icon name="lucideChevronDown"
                    class="group-has-[:checked]:rotate-0 rotate-180 transition-all text-xl"></ng-icon>
                </div>
              </div>
            </label>
            <div
              class="hidden   peer-has-[:checked]:flex  items-center justify-start gap-2 text-xs font-medium transition-all bg-white rounded-3xl md:text-base border-slate-200 text-green-950 group">
              <button (click)="changeReportLanguage('tr')" [disabled]="reportContent.lang == 'tr'"
                class="flex items-center justify-center flex-1 disabled:ring-2 disabled:ring-green-600/40 disabled:border-green-700 px-3 py-2 text-sm font-normal transition-all bg-white border shadow gap-0.5 hover:border-green-600 text-green-950 active:scale-95 group rounded-3xl ">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" class="scale-105" viewBox="0 0 16 16"
                  fill="none">
                  <g clip-path="url(#clip0_986_11131)">
                    <path
                      d="M16 12.0004C16 12.4719 15.8127 12.9241 15.4793 13.2575C15.1459 13.5909 14.6937 13.7782 14.2222 13.7782H1.77778C1.30628 13.7782 0.854097 13.5909 0.520699 13.2575C0.187301 12.9241 0 12.4719 0 12.0004V4.00043C0 3.52894 0.187301 3.07675 0.520699 2.74336C0.854097 2.40996 1.30628 2.22266 1.77778 2.22266H14.2222C14.6937 2.22266 15.1459 2.40996 15.4793 2.74336C15.8127 3.07675 16 3.52894 16 4.00043V12.0004Z"
                      fill="#E30917" />
                    <path
                      d="M7.11001 10.6671C6.40277 10.6671 5.72449 10.3861 5.2244 9.88604C4.7243 9.38594 4.44335 8.70767 4.44335 8.00042C4.44335 7.29318 4.7243 6.6149 5.2244 6.1148C5.72449 5.61471 6.40277 5.33376 7.11001 5.33376C7.69224 5.33376 8.23001 5.52264 8.66868 5.83953C8.36284 5.49164 7.98634 5.21295 7.56429 5.02204C7.14224 4.83113 6.68434 4.7324 6.22113 4.73242C5.3544 4.73242 4.52317 5.07673 3.9103 5.6896C3.29743 6.30247 2.95313 7.13369 2.95313 8.00042C2.95307 8.42962 3.03755 8.85462 3.20176 9.25117C3.36597 9.64771 3.60668 10.008 3.91014 10.3115C4.21361 10.615 4.57389 10.8558 4.97041 11.0201C5.36693 11.1843 5.79193 11.2689 6.22113 11.2689C7.19668 11.2689 8.07001 10.8391 8.66868 10.1613C8.2156 10.4903 7.66995 10.6674 7.11001 10.6671ZM8.84913 8.10264L9.93357 8.35242L10.0313 9.46042L10.6038 8.50664L11.6882 8.75598L10.9576 7.91687L11.5296 6.96264L10.506 7.3982L9.77535 6.55864L9.87313 7.66709L8.84913 8.10264Z"
                      fill="#EEEEEE" />
                  </g>
                  <defs>
                    <clipPath id="clip0_986_11131">
                      <rect width="16" height="16" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </button>
              <button (click)="changeReportLanguage('en')" [disabled]="reportContent.lang == 'en'"
                class="flex items-center  flex-1 justify-center disabled:ring-2 disabled:ring-brand-green-500/40 disabled:border-brand-green-500 px-3 py-2 text-sm font-normal transition-all bg-white border shadow gap-0.5 hover:border-brand-green-600 text-green-950 active:scale-95 group rounded-3xl hover:border-3 ">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 16 12" fill="none"
                  class="sidebar">
                  <g clip-path="url(#clip0_986_11140)">
                    <mask id="mask0_986_11140" style="mask-type: luminance" maskUnits="userSpaceOnUse" x="0" y="0"
                      width="16" height="12">
                      <path d="M0 0H15.995V11.9963H0V0Z" fill="white" />
                    </mask>
                    <g mask="url(#mask0_986_11140)">
                      <path d="M0 0H15.995V11.9963H0V0Z" fill="white" />
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0V11.9963H15.995V0H0Z" fill="#2E42A5" />
                      <mask id="mask1_986_11140" style="mask-type: luminance" maskUnits="userSpaceOnUse" x="0" y="0"
                        width="16" height="12">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0V11.9963H15.995V0H0Z" fill="white" />
                      </mask>
                      <g mask="url(#mask1_986_11140)">
                        <path
                          d="M-1.78125 11.1394L1.73865 12.6284L16.0742 1.61883L17.9311 -0.593476L14.1673 -1.09082L8.32009 3.6532L3.61356 6.8502L-1.78125 11.1394Z"
                          fill="white" />
                        <path
                          d="M-1.30469 12.1826L0.489252 13.0463L17.2595 -0.798828H14.7418L-1.30419 12.1821L-1.30469 12.1826Z"
                          fill="#F50100" />
                        <path
                          d="M17.7748 11.1394L14.2549 12.6284L-0.0805801 1.61883L-1.9375 -0.593476L1.82632 -1.09082L7.6735 3.6532L12.38 6.8502L17.7748 11.1394Z"
                          fill="white" />
                        <path
                          d="M17.6541 11.8882L15.8607 12.7519L8.71993 6.82375L6.60259 6.16195L-2.11719 -0.585938H0.401026L9.1153 6.002L11.4301 6.79576L17.6541 11.8882Z"
                          fill="#F50100" />
                        <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M9.88723 -0.999023H6.11041V3.99941H-0.984375V7.99817H6.11041V12.9966H9.88723V7.99817H17.01V3.99941H9.88723V-0.999023Z"
                          fill="#F50100" />
                        <mask id="mask2_986_11140" style="mask-type: luminance" maskUnits="userSpaceOnUse" x="-1" y="-1"
                          width="19" height="14">
                          <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M9.88723 -0.999023H6.11041V3.99941H-0.984375V7.99817H6.11041V12.9966H9.88723V7.99817H17.01V3.99941H9.88723V-0.999023Z"
                            fill="white" />
                        </mask>
                        <g mask="url(#mask2_986_11140)">
                          <path
                            d="M6.1101 -0.999336V-1.99902H5.11041V-0.999336H6.1101ZM9.88692 -0.999336H10.8866V-1.99902H9.88692V-0.999336ZM6.1101 3.9991V4.99879H7.10978V3.9991H6.1101ZM-0.984687 3.9991V2.99941H-1.98438V3.9991H-0.984687ZM-0.984687 7.99785H-1.98438V8.99754H-0.984687V7.99785ZM6.1101 7.99785H7.10978V6.99817H6.1101V7.99785ZM6.1101 12.9963H5.11041V13.996H6.1101V12.9963ZM9.88692 12.9963V13.996H10.8866V12.9963H9.88692ZM9.88692 7.99785V6.99817H8.88723V7.99785H9.88692ZM17.0097 7.99785V8.99754H18.0094V7.99785H17.0097ZM17.0097 3.9991H18.0094V2.99941H17.0097V3.9991ZM9.88692 3.9991H8.88723V4.99879H9.88692V3.9991ZM6.1101 0.000351787H9.88692V-1.99902H6.1101V0.000351787ZM7.10978 3.9991V-0.999336H5.11041V3.9991H7.10978ZM-0.984687 4.99879H6.1101V2.99941H-0.984687V4.99879ZM0.0150003 7.99785V3.9991H-1.98438V7.99785H0.0150003ZM6.1101 6.99817H-0.984687V8.99754H6.1101V6.99817ZM7.10978 12.9963V7.99785H5.11041V12.9963H7.10978ZM9.88692 11.9966H6.1101V13.996H9.88692V11.9966ZM8.88723 7.99785V12.9963H10.8866V7.99785H8.88723ZM17.0097 6.99817H9.88692V8.99754H17.0097V6.99817ZM16.01 3.9991V7.99785H18.0094V3.9991H16.01ZM9.88692 4.99879H17.0097V2.99941H9.88692V4.99879ZM8.88723 -0.999336V3.9991H10.8866V-0.999336H8.88723Z"
                            fill="white" />
                        </g>
                      </g>
                    </g>
                  </g>
                </svg>
              </button>
            </div>
            <button (click)="reportContent.showOptions = false; downloadDocxById()"
              class="flex items-center justify-start gap-2 px-3 py-2 text-xs font-medium transition-all bg-white border shadow cursor-pointer disabled:scale-95 disabled:border-zinc-200 disabled:opacity-50 md:text-sm active:scale-95 border-slate-200 hover:border-black text-green-950 group rounded-3xl">
              <ng-icon name="lucideDownload" class="text-xl font-semibold"></ng-icon>
              {{t('download')}}
            </button>
            <button (click)="openEditReportDialog()"
              class="flex items-center justify-start gap-2 px-3 py-2 text-xs font-medium transition-all origin-right bg-white border shadow cursor-pointer disabled:scale-95 disabled:border-zinc-200 disabled:opacity-50 md:text-sm active:scale-95 border-slate-200 hover:border-black text-green-950 group rounded-3xl"
              [matTooltip]="">
              <ng-icon name="lucideSquarePen" class="text-xl font-semibold"></ng-icon>
              {{t('edit_report_title')}}
            </button>
            <button (click)="toggleFavorite($event)"
              class="flex items-center justify-start gap-2 px-3 py-2 text-xs font-medium transition-all origin-right bg-white border shadow cursor-pointer disabled:scale-95 disabled:border-zinc-200 disabled:opacity-50 md:text-sm active:scale-95 border-slate-200 hover:border-black text-green-950 group rounded-3xl"
              [matTooltip]="">
              <ng-icon [name]="reportContent.favorite ? 'tablerStarFilled' : 'tablerStarOff'" class="text-2xl"
                [class.text-yellow-500]="reportContent.favorite" [class.fill-yellow-500]="reportContent.favorite"
                [class.text-gray-400]="!reportContent.favorite"></ng-icon>
              {{t('add_remove_favorites')}}
            </button>
            <!-- Maximize/Minimize Toggle -->
            <button (click)="toggleFullscreen(); reportContent.showOptions = false"
              class="flex items-center justify-start gap-2 px-3 py-2 text-xs font-medium transition-all bg-white border shadow disabled:scale-95 disabled:border-zinc-200 disabled:opacity-50 rounded-3xl md:text-sm active:scale-95 border-slate-200 hover:border-black text-green-950 group ">
              <ng-icon [name]="isFullscreen ? 'lucideMinimize' : 'lucideExpand'" class="text-xl"></ng-icon>
              {{isFullscreen ? t('exit_fullscreen') : t('maximize_report')}}
            </button>
            <!--Clone -->
            <button (click)="getReportClone(); reportContent.showOptions = false"
              class="flex items-center justify-start gap-2 px-3 py-2 text-xs font-medium transition-all origin-center bg-white border shadow text-nowrap rounded-3xl md:text-sm active:scale-95 border-slate-200 hover:border-green-500 text-green-950 group ">
              <ng-icon name="lucideRefreshCcw" class="text-xl"></ng-icon>
              {{t('clone_report')}}
            </button>
            <!-- Delete -->
            <button (click)="deleteReport()"
              class="flex items-center justify-start gap-2 px-3 py-2 text-xs font-medium text-red-700 transition-all bg-white border shadow md:text-sm active:scale-95 border-slate-200 hover:border-red-500 group rounded-3xl">
              <ng-icon name="lucideTrash2" class="text-xl"></ng-icon>
              {{t('delete_report')}}
            </button>
          </div>
        </div>
      </div>
      <button *ngIf="isFromMaximize" (click)="close()"
        class=" flex items-center justify-center px-1.5 p-1 text-sm font-normal transition-all bg-white border shadow gap-0.5 hover:border-red-600 text-green-950 active:scale-95 group rounded-3xl hover:border-3 ">
        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="30" viewBox="0 0 24 24" class="rotate-45"
          fill="none">
          <path
            d="M13 5C13 4.44772 12.5523 4 12 4C11.4477 4 11 4.44772 11 5V11H5C4.44772 11 4 11.4477 4 12C4 12.5523 4.44772 13 5 13H11V19C11 19.5523 11.4477 20 12 20C12.5523 20 13 19.5523 13 19V13H19C19.5523 13 20 12.5523 20 12C20 11.4477 19.5523 11 19 11H13V5Z"
            fill="currentColor" />
        </svg>
      </button>


    </div>
  </div>

  <div *ngIf="report && !isLoading" class="px-3 py-2 text-sm text-gray-800 bg-white border border-gray-200 rounded-3xl">
    <p *ngIf="reportContent.description">{{reportContent.description}}</p>
    <p *ngIf="!reportContent.description" class="text-sm italic">{{t('no_description')}}</p>
  </div>

  <div *ngIf="report && !isLoading" class="flex items-start w-full gap-2 overflow-hidden grow">
    <!--Report-->
    <div class="relative flex flex-col w-full h-full overflow-y-auto rounded-2xl" *ngIf="reportContent">



      <div *ngFor="let content of reportContent.contents; let i = index; let last = last"
        class="pb-0 border-b md:border-none" [ngClass]="{'border-none': last}">

        <!-- Table section -->
        <div class="w-full mt-2 mb-2 overflow-x-auto border border-gray-200 rounded-none bg-zinc-100 text-zinc-700"
          [innerHTML]="reportContent.lang=='tr' ? content.tr.table: content.en.table">
        </div>

        <!-- Interpretation section -->
        <div *ngIf="!isMobile" class="pb-2 text-base text-justify transition-all border-b-2"
          [ngClass]="{'border-none': last}">
          <ng-container *ngIf="selectedHighlight && selectedHighlight.tableIndex === i">
            <p [innerHTML]="getHighlightedText(content, i)"></p>
          </ng-container>
          <ng-container *ngIf="!selectedHighlight || selectedHighlight.tableIndex !== i">
            <div [innerHTML]="(reportContent.lang=='tr' ? content.tr.interpret : content.en.interpret) | formulaFormatter"></div>
          </ng-container>
        </div>

        <!-- Mobile view -->
        <app-read-more *ngIf="isMobile" class="pb-2 text-xs text-justify transition-all"
          [content]="(reportContent.lang=='tr' ? content.tr.interpret: content.en.interpret) | formulaFormatter" [limit]="160"
          [completeWords]="true" [allowHtml]="true">
        </app-read-more>
      </div>
      <!-- method -->
      <div *ngIf="reportContent.methods_content" class="pb-2 text-base text-justify transition-all">
        <p *ngIf="reportContent.methods_content.tr != '' || reportContent.methods_content.en != ''"
          class="text-xl font-semibold border-b-2 text-zinc-700">
          {{reportContent.lang=='tr' ? 'İstatistiksel Yöntem' : 'Statistical Method'}}
        </p>
        <div [innerHTML]="(reportContent.lang=='tr' ? reportContent.methods_content.tr : reportContent.methods_content.en) | formulaFormatter"></div>
      </div>

      <div *ngIf="reportContent.warnings" class="pb-2 text-base text-justify transition-all">
        <p *ngIf="reportContent.warnings.tr != '' || reportContent.warnings.en != ''"
          class="flex items-center gap-2 text-xl font-semibold border-b-2 text-status-warning-700 border-b-status-warning-700">
          <ng-icon name="heroExclamationTriangle" class="text-warning-700"></ng-icon>
          {{reportContent.lang=='tr' ? 'Uyarılar' : 'Warnings'}}
        </p>
        <div [innerHTML]="(reportContent.lang=='tr' ? reportContent.warnings.tr : reportContent.warnings.en) | formulaFormatter">
        </div>
      </div>

      <div *ngIf="reportContent.acknowledgements" class="pb-2 text-base text-justify transition-all">
        <p *ngIf="reportContent.acknowledgements.tr != '' || reportContent.acknowledgements.en != ''"
          class="text-xl font-semibold border-b-2 text-zinc-700">
          {{reportContent.lang=='tr' ? 'Teşekkür' : 'Acknowledgements'}}
        </p>
        <div
          [innerHTML]="(reportContent.lang=='tr' ? reportContent.acknowledgements.tr : reportContent.acknowledgements.en) | formulaFormatter">
        </div>
      </div>
      <div *ngIf=" reportContent.credit_usage.tr == ' '" class="flex items-center justify-center w-full">
        <button (click)="openPaymentDialog()"
          class="flex items-center gap-2 p-2 text-base font-medium text-white bg-green-700 shadow-md rounded-3xl ">
          <ng-icon name="ionCartOutline" class="text-xl"></ng-icon>
          {{t('buy_analysis')}}
        </button>
      </div>

    </div>

  </div>
</div>