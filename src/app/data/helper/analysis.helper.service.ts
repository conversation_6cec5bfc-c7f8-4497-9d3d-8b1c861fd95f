import { Injectable } from '@angular/core';
import { AnalysisService } from '@app/data/services/analysis.service';
import { DatasetService } from '@app/data/services/dataset.service';
import { Observable, Subject, map, single } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class AnalysisHelperService {

    constructor(
        private d: DatasetService,
        private a: AnalysisService,
    ) { }
    // Analiz tipleri ve ilgili ayarlar
    // Analizler belirli bir sıra ile yapılır burada order attribute'ü rol oynar,
    // Type attribute'ü ise listelerin seçim veya referans olup olmadığını belirler
    // Options attribute'ü ise hangi ölçüm türlerinin seçilebileceğini belirler
    // Split listesi backend tarafından yönetildiğinde yorum satırlarının açılması yeterli olacaktır

    private analysisConfig = {
        descriptive: {
            fields: [
                { order: 1, name: 'variable_list', type: 'select', options: ['Scale', 'Ordinal', 'Nominal'] },
                { order: 2, name: 'split_list', type: 'select', options: ['Ordinal', 'Nominal'] }
            ]
        },
        single: {
            fields: [
                { order: 1, name: 'variable_list', type: 'reference', options: ['Scale'] },
                { order: 2, name: 'split_list', type: 'select', options: ['Ordinal', 'Nominal'] }
            ]
        },
        multi: {
            fields: [
                { order: 1, name: 'factor_list', type: 'select', options: ['Ordinal', 'Nominal'] },
                { order: 2, name: 'variable_list', type: 'select', options: ['Scale'] },
                { order: 3, name: 'split_list', type: 'select', options: ['Ordinal', 'Nominal'] }
            ]
        },
        dependent: {
            fields: [
                { order: 1, name: 'define_list', type: 'select', options: ['Scale', 'Ordinal'] },
                { order: 2, name: 'split_list', type: 'select', options: ['Ordinal', 'Nominal'] }
            ]
        },
        correlation: {
            fields: [
                { order: 1, name: 'row_list', type: 'select', options: ['Scale', 'Ordinal', 'Nominal'] },
                { order: 2, name: 'column_list', type: 'select', options: ['Scale', 'Ordinal', 'Nominal'] },
                { order: 3, name: 'split_list', type: 'select', options: ['Ordinal', 'Nominal'] }
            ]
        },
        chisq: {
            fields: [
                { order: 1, name: 'row_list', type: 'select', options: ['Ordinal', 'Nominal'] },
                { order: 2, name: 'column_list', type: 'select', options: ['Ordinal', 'Nominal'] },
                { order: 3, name: 'split_list', type: 'select', options: ['Ordinal', 'Nominal'] }
            ]
        },
        comean: {
            fields: [
                { order: 1, name: 'factor_list', type: 'select', options: ['Ordinal', 'Nominal'] },
                { order: 2, name: 'dependent_list', type: 'select', options: ['Scale'] },
                { order: 3, name: 'covariate_list', type: 'select', options: ['Scale'] },
                { order: 4, name: 'split_list', type: 'select', options: ['Ordinal', 'Nominal'] }
            ]
        },
        logistic_cox: {
            fields: [
                { order: 1, name: 'status_list', type: 'select', options: ['Nominal'] },
                { order: 2, name: 'covariate_list', type: 'select', options: ['Scale', 'Ordinal', 'Nominal'] },
                { order: 3, name: 'time_list', type: 'select', options: ['Scale'] },
                { order: 4, name: 'split_list', type: 'select', options: ['Ordinal', 'Nominal'] }
            ]
        },
        survival: {
            fields: [
                { order: 1, name: 'status_list', type: 'select', options: ['Nominal'] },
                { order: 2, name: 'factor_list', type: 'select', options: ['Ordinal', 'Nominal'] },
                { order: 3, name: 'time_list', type: 'select', options: ['Scale'] },
                { order: 4, name: 'strata_list', type: 'select', options: ['Ordinal', 'Nominal'] }
            ]
        },
        roc: {
            fields: [
                { order: 1, name: 'dependent_list', type: 'select', options: ['Nominal'] },
                { order: 2, name: 'independent_list', type: 'select', options: ['Scale', 'Ordinal', 'Nominal'] }, // Değişiklik: Tüm türlere izin ver
                { order: 3, name: 'split_list', type: 'select', options: ['Ordinal', 'Nominal'] }
            ]
        },
        linear: {
            fields: [
                { order: 1, name: 'dependent_list', type: 'select', options: ['Scale'] },
                { order: 2, name: 'independent_list', type: 'select', options: ['Scale', 'Ordinal', 'Nominal'] },
                { order: 3, name: 'split_list', type: 'select', options: ['Ordinal', 'Nominal'] }
            ]
        }
    };
    // Analiz tiplerine göre icon listesi
    private iconList = {
        descriptive: 'assets/icons/descriptive.svg',
        single: 'assets/icons/single.svg',
        multi: 'assets/icons/multi.svg',
        dependent: 'assets/icons/dependent.svg',
        correlation: 'assets/icons/correlation.svg',
        chisq: 'assets/icons/chisq.svg',
        comean: 'assets/icons/comean.svg',
        logistic_cox: 'assets/icons/logistic_cox.svg',
        survival: 'assets/icons/survival.svg',
        roc: 'assets/icons/roc.svg',
        linear: 'assets/icons/linear.svg'
    }
    // Analiz tiplerine göre icon listesinden icon döndürür
    getIcon(analysisType: string): string {
        return this.iconList[analysisType];
    }
    // Veri seti id'sine göre değişkenleri getirir
    getVariables(datasetId: string): Observable<any> {
        return this.d.getDatasetById(datasetId).pipe(
            map(data => {
                const datasetName = data.name;
                const variables = data.variables
                return {
                    analyses: data.analyses,
                    datasetName,
                    variables
                };
            })
        );
    }
    // Analiz tiplerine göre liste uzunluğunu döndürür
    getOrderCount(analysisType: string): number {
        return this.analysisConfig[analysisType].fields.length;
    }
    // Analiz tiplerine göre ayarları döndürür
    getConfiguration(analysisType: string) {
        return this.analysisConfig[analysisType];
    }
    // Analiz süreci için dizileri ayarlar
    processVariables(variables: any[], analysisType: string, params?: any): any {
        const config = this.getConfiguration(analysisType);
        const filteredVariables = {};
        for (const field of config.fields) {
            const listVariables = variables.filter(variable =>
                field.options.includes(variable.measure) &&
                (!('import' in variable) || variable.import === true)
            );
            const filteredOptions = {
                name: field.name,
                variable: listVariables.map((item) => {
                    // Check if params exists and has the field name
                    let selected = false;
                    let referenceValue = null;

                    if (params) {
                        // Check if params has the field name directly
                        if (params[field.name] && Array.isArray(params[field.name])) {
                            selected = params[field.name].includes(item.id);
                        }

                        // For single analysis, check for reference values
                        if (selected && field.name === 'variable_list' && params['reference_list'] && Array.isArray(params['reference_list'])) {
                            const index = params['variable_list'].indexOf(item.id);
                            if (index !== -1 && index < params['reference_list'].length) {
                                referenceValue = String(params['reference_list'][index]);
                            }
                        }
                    }
                    const disabled = item.measure === 'Nominal' && item.value_labels === null;

                    return { ...item, selected, disabled, referenceValue };
                }),
                type: field.type
            };

            filteredVariables[field.order] = filteredOptions;
        }
        let additional = null;
        if (analysisType === 'dependent' && params) {
            // For dependent analysis, we need to handle the paired_list structure
            if (params.paired_list) {
                additional = params.paired_list;
            } else {
                additional = params;
            }
        }
        return { filteredVariables, additional };
    }
    // Veri setindeki değişkenlerin tiplerine göre düzenleme yapar
    checkMeasureOptions(list: any[], analysisType: string, step: number): { type: string, disable: boolean }[] {
        const config = this.getConfiguration(analysisType);
        const options = config.fields.find(field => field.order === step).options
        const measures = list.map(item => item.measure);
        const result = [];
        for (const option of options) {
            const disable = !measures.includes(option);
            result.push({ type: option, disable, checked: false });
        }
        return result;
    }
    // İlgili analiz tipine göre payload oluşturur analiz isteği gönderir
    payloadHelper(analysisType: string, analysisId: string, variables: any, options?: any): any {
        const config = this.getConfiguration(analysisType);
        const payload: any = {
            analysis_id: analysisId
        };

        // Extract additionalOptions and analysisConfiguration from options
        const additionalOptions = options?.additionalOptions;
        const analysisConfiguration = options?.analysisConfiguration;

        // If analysisConfiguration is provided, add it to the payload
        if (analysisConfiguration) {
            payload['analysis_configuration'] = analysisConfiguration;
        }
        // Dependent için özel bir payload oluşturulur
        if (analysisType === 'dependent') {
            payload['paired_list'] = {
                times: variables.defineTimes,
                insets: variables.multiDefineList.reverse().map(item => {
                    return {
                        ind: item.ind,
                        variable_list: item.timeTable.sub.map(time =>
                            time.variableId,
                        ),
                        define: {
                            tr: item.defineTr,
                            en: item.defineEn
                        },
                        times_ind: item.inset_ind
                    }
                }
                ),
                split_list: variables.splitList.map(split => split.id)
            }
        } else {
            for (const field of config.fields) {
                const list = variables[field.order].variable.filter(item => item.selected);
                payload[field.name] = list.map(item => item.id);
                // Eğer referans tipinde bir değişken varsa referans listesini oluşturur
                if (field.type === 'reference') {
                    payload['reference_list'] = list.map(item => {
                        // Always convert comma to dot for backend processing
                        const referenceValue = item.referenceValue.replace(',', '.');
                        return Number(referenceValue);
                    });
                }
                // Eğer chisq analizi ise raporlama tipini belirler
                if (analysisType === 'chisq') {
                    payload['report_by'] = additionalOptions;
                }
                // Eğer comean analizi ise raporlama tipini belirler
                if (analysisType === 'comean') {
                    payload['time'] = additionalOptions;
                }
            }
        }
        return payload;
    }

    sendAnalysisRequest(analysisType: string, analysisId: string, variables: any, options?: any) {
        const payload = this.payloadHelper(analysisType, analysisId, variables, options);
        switch (analysisType) {
            case 'descriptive':
                return this.a.createDescriptiveAnalysis(payload);
            case 'single':
                return this.a.createIndependentSingleAnalysis(payload);
            case 'multi':
                return this.a.createIndependentMultiAnalysis(payload);
            case 'dependent':
                return this.a.createDependentAnalysis(payload);
            case 'correlation':
                return this.a.createCorrelationAnalysis(payload);
            case 'chisq':
                return this.a.createChiSquareAnalysis(payload);
            case 'comean':
                return this.a.createMeanComparisonAnalysis(payload);
            case 'logistic_cox':
                return this.a.createLogisticCoxAnalysis(payload);
            case 'survival':
                return this.a.createSurvivalAnalysis(payload);
            case 'roc':
                return this.a.createRocAnalysis(payload);
            case 'linear':
                return this.a.createLinearAnalysis(payload);
            default:
                return null;
        }
    }
    sendCloneAnalysisRequest(analysisType: string, analysisId: string, variables: any, reportId: any, options?: any) {
        // Convert frontend analysis type names to backend names
        let backendAnalysisType = analysisType;
        if (analysisType === 'logistic_cox') {
            backendAnalysisType = 'binary';
        } else if (analysisType === 'linear') {
            backendAnalysisType = 'regression';
        }

        // Use the converted backend analysis type
        var payload = this.payloadHelper(analysisType, analysisId, variables, options);
        payload['analysis_group'] = backendAnalysisType;  // Use backend name here
        return this.a.updateAnalysisRequest(reportId, payload);
    }
    calculateCredits(analysisType: string, analysisId: string, variables: any, options?: any): any {
        const payload = this.payloadHelper(analysisType, null, variables, options);
        payload['analysis_group'] = analysisType;
        return this.a.getCalculatedAnalysisCredits(payload);
    }
    setAnalysisConfigurations(cid: number, reportFormat: number, zeroCount: number) {
        const payload = {
            separator: reportFormat == 1 ? ',' : '.', // 1: ',' 2: '.'
            precision: zeroCount,
        }
        return this.a.setAnalysisConfigurations(payload, cid);
    }

    getReportAnalysisRequest() {
        return this.report_info.asObservable();
    }
    report_info = new Subject<any>();

    getAnalysisTitle(analysisType: string): string {
        switch (analysisType) {
            case 'descriptive':
                return 'analyses.titles.descriptive';
            case 'single':
                return 'analyses.titles.single';
            case 'multi':
                return 'analyses.titles.multi';
            case 'dependent':
                return 'analyses.titles.dependent';
            case 'correlation':
                return 'analyses.titles.correlation';
            case 'chisq':
                return 'analyses.titles.chisq';
            case 'comean':
                return 'analyses.titles.comean';
            case 'logistic_cox':
                return 'analyses.titles.logistic_cox';
            case 'survival':
                return 'analyses.titles.survival';
            case 'roc':
                return 'analyses.titles.roc';
            case 'linear':
                return 'analyses.titles.linear';
            default:
                return 'analyses.titles.unknown';
        }
    }

    getAnalysisDescription(analysisType: string): string {
        switch (analysisType) {
            case 'descriptive':
                return 'analyses.descriptions.descriptive';
            case 'single':
                return 'analyses.descriptions.single';
            case 'multi':
                return 'analyses.descriptions.multi';
            case 'dependent':
                return 'analyses.descriptions.dependent';
            case 'correlation':
                return 'analyses.descriptions.correlation';
            case 'chisq':
                return 'analyses.descriptions.chisq';
            case 'comean':
                return 'analyses.descriptions.comean';
            case 'logistic_cox':
                return 'analyses.descriptions.logistic_cox';
            case 'survival':
                return 'analyses.descriptions.survival';
            case 'roc':
                return 'analyses.descriptions.roc';
            case 'linear':
                return 'analyses.descriptions.linear';
            default:
                return 'analyses.descriptions.unknown';
        }
    }

    getCreditExplanation(analysisType: string): string {
        switch (analysisType) {
            case 'descriptive':
                return 'analyses.credit_explanations.descriptive';
            case 'single':
                return 'analyses.credit_explanations.single';
            case 'multi':
                return 'analyses.credit_explanations.multi';
            case 'dependent':
                return 'analyses.credit_explanations.dependent';
            case 'correlation':
                return 'analyses.credit_explanations.correlation';
            case 'chisq':
                return 'analyses.credit_explanations.chisq';
            case 'comean':
                return 'analyses.credit_explanations.comean';
            case 'logistic_cox':
                return 'analyses.credit_explanations.logistic_cox';
            case 'survival':
                return 'analyses.credit_explanations.survival';
            case 'roc':
                return 'analyses.credit_explanations.roc';
            case 'linear':
                return 'analyses.credit_explanations.linear';
            default:
                return 'analyses.credit_explanations.default';
        }
    }
}