import { Analysis } from "./analysis.interface";

// Variable interface'ini ekleyin
export interface Variable {
  id: number;
  header: string;
  name: string;
  measure: string;
  value_labels: { [key: string]: number } | null;
  created_at: string;
  updated_at: string;
}

export interface Dataset {
  id: number;
  name: string;
  s3_url: string;
  project_id: number;
  variables_json: any;
  diagnosed_s3_url: string | null;
  created_at: string;
  updated_at: string;
  analyses: Analysis[];
  variables?: Variable[]; // Bu satırı ekleyin - API response'da geliyor
}

export interface DatasetInfo {
  name: string;
  rowCount: number;
  variableCount: number;
  s3_url?: string;
}

export interface DataColumn {
  id: string;
  name: string;
  type: 'scale' | 'nominal' | 'ordinal';
  typeLabel: string;
  typeColor: string;
}

export interface ValueLabel {
  value: number;
  labelTR: string;
  labelEN: string;
  count: number;
}

export interface DataVariable {
  id: string;
  name: string;
  labelTR: string;
  labelEN: string;
  type: 'scale' | 'nominal' | 'ordinal';
  typeLabel: string;
  typeColor: string;
  description: string;
  missingValues: number;
  uniqueValues: number;
  min?: number;
  max?: number;
  mean?: number;
  median?: number;
  stdDev?: number;
  valueLabels?: ValueLabel[];
}

export interface DataRow {
  [key: string]: any;
}