import { Injectable } from '@angular/core';
import { TranslocoService } from '@ngneat/transloco';

interface VariableRequirement {
  minCount?: number;
  maxCount?: number;
  optional?: boolean;
  isBinary?: boolean;
  types: string[];
}

@Injectable({
  providedIn: 'root'
})
export class AnalysisRequirementsService {
  constructor(
    private transloco: TranslocoService
  ) { }

  private requirements: Record<string, VariableRequirement[]> = {
    // Descriptive: minimum 1 adet değişken olmalı
    'descriptive': [
      { types: ['nominal', 'ordinal', 'scale'], minCount: 1 }
    ],

    // Independent Single Group: minimum 1 adet scale türünde değişken olmalı
    'single': [
      { types: ['scale'], minCount: 1 }
    ],

    // Independent Multi Group: minimum 1 tane scale türünde değişken ve minimum 1 tane Kategorik (Nominal veya Ordinal) türünde değişken olmalı
    'multi': [
      { types: ['scale'], minCount: 1 },
      { types: ['nominal', 'ordinal'], minCount: 1 }
    ],

    // Dependent: minimum 2 adet scale türünde değişken olmalı
    'dependent': [
      { types: ['scale'], minCount: 2 }
    ],

    // Correlation: minimum 2 adet değişken olmalı
    'correlation': [
      { types: ['nominal', 'ordinal', 'scale'], minCount: 2 }
    ],

    // Chisq: minimum 2 adet Kategorik (Nominal veya Ordinal) türünde değişken olmalı
    'chisq': [
      { types: ['nominal', 'ordinal'], minCount: 2 }
    ],

    // Comean: minimum 1 adet scale türünde değişken ve minimum 1 adet Kategorik (Nominal veya Ordinal) türünde değişken olmalı
    'comean': [
      { types: ['scale'], minCount: 1 },
      { types: ['nominal', 'ordinal'], minCount: 1 }
    ],

    // Binary (Logistic/Cox): Status list + Covariate list gerekli, Time list opsiyonel
    // Status: minimum 1 adet nominal türünde ve binary (2 adet value label içermeli) değişken olmalı
    // Covariate: minimum 1 adet değişken olmalı
    // Time: opsiyonel, en fazla 1 adet scale türünde değişken olmalı
    'logistic_cox': [
      { types: ['nominal'], minCount: 1, isBinary: true }, // Status list
      { types: ['nominal', 'ordinal', 'scale'], minCount: 1 }, // Covariate list
      { types: ['scale'], minCount: 0, maxCount: 1, optional: true } // Time list (opsiyonel)
    ],

    // Survival: Status + Factor + Time gerekli, Strata opsiyonel
    // Status: 1 adet nominal türünde ve binary (2 adet value label içermeli) değişken olmalı
    // Factor: minimum 1 adet Kategorik (Nominal veya Ordinal) türünde değişken olmalı
    // Time: 1 adet scale türünde değişken olmalı
    // Strata: opsiyonel, en fazla 1 adet Kategorik (Nominal veya Ordinal) türünde değişken olmalı
    'survival': [
      { types: ['nominal'], minCount: 1, maxCount: 1, isBinary: true }, // Status
      { types: ['nominal', 'ordinal'], minCount: 1 }, // Factor
      { types: ['scale'], minCount: 1, maxCount: 1 }, // Time
      { types: ['nominal', 'ordinal'], minCount: 0, maxCount: 1, optional: true } // Strata (opsiyonel)
    ],

    // ROC: Dependent + Independent gerekli
    // Dependent: minimum 1 adet nominal türünde ve binary (2 adet value label içermeli) değişken olmalı
    // Independent: minimum 1 adet scale türünde değişken olmalı
    'roc': [
      { types: ['nominal'], minCount: 1, isBinary: true }, // Dependent
      { types: ['scale'], minCount: 1 } // Independent
    ],

    // Linear: Dependent + Independent gerekli
    // Dependent: minimum 1 adet scale türünde değişken olmalı
    // Independent: minimum 1 adet değişken içermeli
    'linear': [
      { types: ['scale'], minCount: 1 }, // Dependent
      { types: ['nominal', 'ordinal', 'scale'], minCount: 1 } // Independent
    ]
  };

  // Analiz türleri için değişken tekrarı kontrol edilmesi gerekenler
  private requireUniqueVariables = ['binary', 'survival', 'roc', 'linear'];

  // Overload: Eski kullanım için (tek değişken listesi)
  checkRequirements(analysisType: string, variables: any[]): boolean;
  // Overload: Yeni kullanım için (çoklu değişken listeleri)
  checkRequirements(analysisType: string, variableLists: any[][], allVariables: any[]): boolean;

  checkRequirements(analysisType: string, variablesOrLists: any[] | any[][], allVariables?: any[]): boolean {
    const requirements = this.requirements[analysisType];
    if (!requirements) return false;

    // Eski kullanım: tek değişken listesi
    if (!Array.isArray(variablesOrLists[0]) || allVariables === undefined) {
      const variables = variablesOrLists as any[];

      for (const req of requirements) {
        const matchingVars = variables.filter(v =>
          req.types.includes(v.measure.toLowerCase())
        );

        if (matchingVars.length < (req.minCount || 0)) {
          return false;
        }

        // Binary kontrolü
        if (req.isBinary) {
          const binaryVariables = matchingVars.filter(v => this.isBinaryVariable(v));
          if (binaryVariables.length !== matchingVars.length) {
            return false;
          }
        }
      }

      return true;
    }

    // Yeni kullanım: çoklu değişken listeleri
    const variableLists = variablesOrLists as any[][];

    // Değişken tekrarı kontrolü (belirli analiz türleri için)
    if (this.requireUniqueVariables.includes(analysisType)) {
      if (!this.checkUniqueVariables(variableLists)) {
        return false;
      }
    }

    // Her gereksinim için kontrol
    for (let i = 0; i < requirements.length; i++) {
      const req = requirements[i];
      const variables = variableLists[i] || [];

      // Minimum sayı kontrolü
      if (variables.length < (req.minCount || 0)) {
        return false;
      }

      // Maksimum sayı kontrolü
      if (req.maxCount && variables.length > req.maxCount) {
        return false;
      }

      // Tür kontrolü
      const validVariables = variables.filter(v =>
        req.types.includes(v.measure.toLowerCase())
      );

      if (validVariables.length !== variables.length) {
        return false;
      }

      // Binary kontrolü
      if (req.isBinary) {
        const binaryVariables = variables.filter(v => this.isBinaryVariable(v));
        if (binaryVariables.length !== variables.length) {
          return false;
        }
      }
    }

    return true;
  }

  // Değişkenin binary olup olmadığını kontrol et (2 adet value label olmalı)
  private isBinaryVariable(variable: any): boolean {
    return variable.valueLabels &&
      Array.isArray(variable.valueLabels) &&
      variable.valueLabels.length === 2;
  }

  // Değişken listelerinde tekrar eden değişken olup olmadığını kontrol et
  private checkUniqueVariables(variableLists: any[][]): boolean {
    const allSelectedVariables: any[] = [];

    for (const list of variableLists) {
      if (list && list.length > 0) {
        for (const variable of list) {
          // Aynı değişken daha önce seçilmiş mi kontrol et
          if (allSelectedVariables.some(v => v.id === variable.id || v.name === variable.name)) {
            return false;
          }
          allSelectedVariables.push(variable);
        }
      }
    }

    return true;
  }

  getRequirementDescription(analysisType: string): string {
    const lang = this.transloco.getActiveLang();
    const reqs = this.requirements[analysisType];
    if (!reqs) return '';

    let description = reqs.map(req => {
      const types = req.types.join(lang === 'tr' ? ' veya ' : ' or ');
      let desc = `${req.minCount || 0} ${this.transloco.translate('dataset_view.variables')} ${types}`;

      if (req.isBinary) {
        desc += lang === 'tr' ? ' (2 değerli)' : ' (binary)';
      }

      if (req.optional) {
        desc += lang === 'tr' ? ' (opsiyonel)' : ' (optional)';
      }

      return desc;
    }).join(lang === 'tr' ? ' ve ' : ' and ');

    // Değişken tekrarı uyarısı ekle
    if (this.requireUniqueVariables.includes(analysisType)) {
      const uniqueWarning = lang === 'tr'
        ? 'Her adımda farklı değişken seçilmelidir.'
        : 'Each step must use different variables.';
      description += ` ${uniqueWarning}`;
    }

    return description;
  }

  // Çoklu liste kontrolü için ayrı metod
  checkMultipleRequirements(analysisType: string, variableLists: any[][], allVariables: any[]): boolean {
    return this.checkRequirements(analysisType, variableLists, allVariables);
  }
}