//! DON'T PRETIFY THIS FILE

@import "assets/styles/ng-alt-snotify/material.scss";
@import '~handsontable/dist/handsontable.full.css';


@tailwind base;
@tailwind components;
@tailwind utilities;
// ng-select
@import "~@ng-select/ng-select/themes/default.theme.css";

.mdc-notched-outline__notch {
  border-right: none !important;
}

@import "@angular/material/prebuilt-themes/indigo-pink.css";
// Import Angular Material theming functions
@import '@angular/material/theming';

/* Font face tanımlaması */

/* Base font definition */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Formula formatting styles */
.formula-variable {
  font-style: italic;
  font-weight: 500;
  color: #1f2937;
}

.formula-superscript {
  font-size: 0.75em;
  vertical-align: super;
  color: #374151;
}

.formula-subscript {
  font-size: 0.75em;
  vertical-align: sub;
  color: #374151;
}

.formula-greek {
  font-style: italic;
  font-weight: 600;
  color: #1e40af;
}

.formula-stat-symbol {
  font-style: italic;
  font-weight: 600;
  color: #dc2626;
}

.formula-operator {
  font-weight: 600;
  color: #059669;
  margin: 0 2px;
}

.formula-value {
  font-weight: 500;
  color: #7c2d12;
}

.formula-parentheses {
  color: #6b7280;
}

.formula-block {
  font-style: italic;
  font-weight: 600;
  color: #1e40af;
  font-family: 'Times New Roman', serif;
}

/* Optional: Define utility classes for weights */

/* Font ailesi için fallback tanımlaması */
:root {
  --font-family-sans: 'Baloo 2', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

body {
  font-family: var(--font-family-sans);
}


.iti {
  width: 100% !important;
}

.iti__flag-container {
  right: 0 !important;
}

.iti--allow-dropdown input {
  width: 100%;
}

.country-dropdown {
  width: 100% !important;
}

.iti__country-list {
  width: 100%;
}

.iti__country-name {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

html {
  scroll-behavior: smooth;
}


app-eft-confirm {
    height: 100%;
    display: flex;
    flex-direction: column;
}
app-dashboard {
    height: 100%;
    display: flex;
    flex-direction: column;
}
app-users {
    height: 100%;
    display: flex;
    flex-direction: column;
}
app-user-list {
    height: 100%;
    display: flex;
    flex-direction: column;
}
app-credit-card-transactions{
    height: 100%;
    display: flex;
    flex-direction: column;
}


.mdc-text-field--outlined {
  --mdc-outlined-text-field-container-shape: 28px !important;
}

.primary-blue-button {
  @apply 
    text-white 
    bg-brand-blue-500 
    shadow-[inset_0_1px_rgba(255,255,255,0.15),0_4px_10px_rgba(0,60,189,0.3)]
    hover:bg-brand-blue-600 
    hover:shadow-[0_6px_12px_-5px_rgba(0,60,189,0.3)]
    active:shadow-[inset_0_2px_4px_rgba(0,0,0,0.2)]
    transition-all
    px-4 sm:px-6 
    py-2 sm:py-3
    text-sm sm:text-base
    inline-flex 
    items-center 
    gap-1 sm:gap-2
    font-medium
    rounded-full
    disabled:opacity-50
    disabled:cursor-not-allowed
    disabled:hover:bg-brand-blue-500 
    disabled:hover:shadow-[inset_0_1px_rgba(255,255,255,0),0_4px_10px_rgba(0,60,189,0)]
    disabled:shadow-[inset_0_1px_rgba(255,255,255,0),0_4px_10px_rgba(0,60,189,0)]
    disabled:active:shadow-[inset_0_2px_4px_rgba(0,0,0,0)]
}
.primary-green-button {
  @apply 
    text-white 
    bg-brand-green-500 
    shadow-[inset_0_1px_rgba(255,255,255,0.15),0_4px_10px_rgba(25,196,128,0.3)]
    hover:bg-[#16B073]
    hover:shadow-[0_6px_12px_-5px_rgba(0,60,189,0.3)]
    active:shadow-[inset_0_2px_4px_rgba(0,0,0,0.15)] 
    transition-all
    px-4 sm:px-6 
    py-2 sm:py-3
    text-sm sm:text-base
    inline-flex 
    items-center 
    gap-1 sm:gap-2
    font-medium
    rounded-full
    disabled:opacity-50
    disabled:cursor-not-allowed
    disabled:hover:bg-brand-green-500 
    disabled:hover:shadow-[inset_0_1px_rgba(255,255,255,0),0_4px_10px_rgba(0,60,189,0)]
    disabled:shadow-[inset_0_1px_rgba(255,255,255,0),0_4px_10px_rgba(0,60,189,0)]
    disabled:active:shadow-[inset_0_2px_4px_rgba(0,0,0,0)]
}

.secondary-blue-button {
  @apply
    px-4 sm:px-6
    py-2 sm:py-3
    text-sm sm:text-base
    rounded-full
    text-brand-blue-500
    inline-flex
    items-center
    gap-1 sm:gap-2
    font-medium
    bg-gradient-to-b 
    from-brand-blue-100 
    to-brand-blue-50 
    border-2 
    border-brand-blue-300
    shadow-[inset_0_1px_rgba(255,255,255,1.0),0_4px_10px_rgba(0,82,249,0.08)]
    hover:from-[#EBF2FF] 
    hover:to-[#E1EBFF] 
    active:shadow-[inset_0_2px_4px_rgba(0,82,249,0.15)] 
    transition-all
    disabled:opacity-50
    disabled:cursor-not-allowed
    disabled:hover:from-brand-blue-100
    disabled:hover:to-brand-blue-50
    disabled:hover:shadow-[inset_0_1px_rgba(255,255,255,0),0_4px_10px_rgba(0,82,249,0)]
    disabled:shadow-[inset_0_1px_rgba(255,255,255,0),0_4px_10px_rgba(0,82,249,0)]
    disabled:active:shadow-[inset_0_2px_4px_rgba(0,82,249,0)]
}
.secondary-green-button {
  @apply
    px-4 sm:px-6
    py-2 sm:py-3
    text-sm sm:text-base
    rounded-full
    text-brand-green-500
    inline-flex
    items-center
    gap-1 sm:gap-2
    font-medium
    bg-gradient-to-b 
    from-brand-green-100 
    to-brand-green-50 
    border-2 
    border-brand-green-300
    shadow-[inset_0_1px_rgba(255,255,255,1.0),0_4px_10px_rgba(25,196,128,0.08)]
    hover:from-[#EBF7F2] 
    hover:to-[#E1F3EC] 
    active:shadow-[inset_0_2px_4px_rgba(25,196,128,0.15)] 
    transition-all
    disabled:opacity-50
    disabled:cursor-not-allowed
    disabled:hover:from-brand-green-100
    disabled:hover:to-brand-green-50
    disabled:hover:shadow-[inset_0_1px_rgba(255,255,255,0),0_4px_10px_rgba(0,82,249,0)]
    disabled:shadow-[inset_0_1px_rgba(255,255,255,0),0_4px_10px_rgba(0,82,249,0)]
    disabled:active:shadow-[inset_0_2px_4px_rgba(0,82,249,0)]
}

.text-blue-button {
  @apply
  flex 
  items-center 
  gap-1 sm:gap-2 
  px-4 sm:px-6 
  py-2 sm:py-3 
  text-sm sm:text-base
  font-medium 
  transition-all 
  rounded-full 
  text-brand-blue-600 
  hover:bg-brand-blue-200
}
.text-green-button {
  @apply
  flex 
  items-center 
  gap-1 sm:gap-2 
  px-4 sm:px-6 
  py-2 sm:py-3 
  text-sm sm:text-base
  font-medium 
  transition-all 
  rounded-full 
  text-brand-green-500 
  hover:bg-brand-green-200
}
.primary-status-error-button {
  @apply 
    text-white 
    bg-status-error-500 
    shadow-[inset_0_1px_rgba(255,255,255,0.15),0_4px_10px_rgba(255,71,71,0.3)]
    hover:bg-status-error-600
    hover:shadow-[0_6px_12px_-5px_rgba(255,71,71,0.3)]
    active:shadow-[inset_0_2px_4px_rgba(0,0,0,0.2)]
    transition-all
    px-4 sm:px-6 
    py-2 sm:py-3
    text-sm sm:text-base
    inline-flex 
    items-center 
    gap-1 sm:gap-2
    font-medium
    rounded-full
    disabled:opacity-50
    disabled:cursor-not-allowed
    disabled:hover:bg-status-error-500
    disabled:hover:shadow-none
    disabled:shadow-none
    disabled:active:shadow-none
}

.secondary-status-error-button {
  @apply
    px-4 sm:px-6
    py-2 sm:py-3
    text-sm sm:text-base
    rounded-full
    text-status-error-500
    inline-flex
    items-center
    gap-1 sm:gap-2
    font-medium
    bg-gradient-to-b 
    from-status-error-100 
    to-status-error-50 
    border-2 
    border-status-error-300
    shadow-[inset_0_1px_rgba(255,255,255,1.0),0_4px_10px_rgba(255,71,71,0.08)]
    hover:from-[#FFF0F0]
    hover:to-[#FFE6E6]
    active:shadow-[inset_0_2px_4px_rgba(255,71,71,0.15)]
    transition-all
    disabled:opacity-50
    disabled:cursor-not-allowed
    disabled:hover:from-status-error-100
    disabled:hover:to-status-error-50
    disabled:hover:shadow-none
    disabled:shadow-none
    disabled:active:shadow-none
}

.secondary-status-warning-button {
  @apply
    px-4 sm:px-6
    py-2 sm:py-3
    text-sm sm:text-base
    rounded-full
    text-status-warning-500
    inline-flex
    items-center
    gap-1 sm:gap-2
    font-medium
    bg-gradient-to-b 
    from-status-warning-25 
    to-status-warning-50
    border-2 
    border-status-warning-75
    shadow-[inset_0_1px_rgba(255,255,255,1.0),0_4px_10px_rgba(255,71,71,0.08)]
    hover:from-[#fefef4]
    hover:to-[#fff9d8]
    active:shadow-[inset_0_2px_4px_rgba(255,71,71,0.15)]
    transition-all
    disabled:opacity-50
    disabled:cursor-not-allowed
    disabled:hover:from-status-warning-100
    disabled:hover:to-status-warning-100
    disabled:hover:shadow-none
    disabled:shadow-none
    disabled:active:shadow-none
}

.text-status-error-button {
  @apply
  flex 
  items-center 
  gap-1 sm:gap-2 
  px-4 sm:px-6 
  py-2 sm:py-3 
  text-sm sm:text-base
  font-medium 
  transition-all 
  rounded-full 
  text-status-error-500 
  hover:bg-status-error-200
}
.text-neutral-button {
  @apply
  flex 
  items-center 
  gap-1 sm:gap-2 
  px-4 sm:px-6 
  py-2 sm:py-3 
  text-sm sm:text-base
  font-medium 
  transition-all 
  rounded-full 
  text-neutral-600 
  hover:bg-neutral-200
}

.neutral-button {
  @apply
    px-4 sm:px-6
    py-2 sm:py-3
    text-sm sm:text-base
    rounded-full
    text-neutral-600
    inline-flex
    items-center
    gap-1 sm:gap-2
    font-medium
    bg-gradient-to-b 
    from-neutral-100 
    to-neutral-50 
    border-2 
    border-neutral-300
    shadow-[inset_0_1px_rgba(255,255,255,1.0),0_4px_10px_rgba(0,0,0,0.08)]
    hover:from-[#F9FAFB]
    hover:to-[#F4F5F7]
    active:shadow-[inset_0_2px_4px_rgba(0,0,0,0.15)]
    transition-all
    disabled:opacity-50
    disabled:cursor-not-allowed
    disabled:hover:from-neutral-100
    disabled:hover:to-neutral-50
    disabled:hover:shadow-none
    disabled:shadow-none
    disabled:active:shadow-none
}